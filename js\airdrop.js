class AirdropAllocator {
    constructor() {
        this.apiKey = (window.apiConfig && window.apiConfig.endpoints.etherscan.apiKey) || 'YourEtherscanAPIKey';
        this.baseUrl = 'https://api.etherscan.io/api';
    }

    async fetchGasForAddress(address) {
        const apiUrl = `${this.baseUrl}?module=account&action=txlist&address=${address}&startblock=0&endblock=********&sort=asc&apikey=${this.apiKey}`;
        const proxyUrl = `cors-proxy.php?url=${encodeURIComponent(apiUrl)}`;
        const res = await fetch(proxyUrl);
        if (!res.ok) throw new Error('Failed to fetch transactions');
        const data = await res.json();
        if (data.status !== '1' || !Array.isArray(data.result)) return { gasUsed: 0, txCount: 0 };

        // Sum gasUsed * gasPrice in wei; also sum gasUsed
        let totalGasUsed = 0n;
        for (const tx of data.result) {
            try {
                const gasUsed = BigInt(tx.gasUsed || '0');
                totalGasUsed += gasUsed; // for tiering we only need gasUsed count
            } catch (_) {}
        }
        return { gasUsed: Number(totalGasUsed), txCount: data.result.length };
    }

    // Calculate exact allocation based on gas usage using the provided formula
    allocationForGas(gasEth, address) {
        // Check if we already have a saved allocation for this address
        const savedAllocation = this.getSavedAllocation(address);
        if (savedAllocation !== null) {
            return savedAllocation.toFixed(2);
        }

        // If no gas spent, allocation is zero
        if (gasEth === 0) {
            this.saveAllocation(address, 0);
            return "0.00";
        }

        let allocation;

        if (gasEth > 0 && gasEth <= 1) {
            // 0.001e-1eth = 2k-10k $Drip
            const min = 2000;
            const max = 10000;
            allocation = min + (gasEth / 1) * (max - min);
        } else if (gasEth > 1 && gasEth <= 10) {
            // 1.1e-10eth = 10k-50k $Drip
            const min = 10000;
            const max = 50000;
            allocation = min + ((gasEth - 1) / 9) * (max - min);
        } else if (gasEth > 10 && gasEth <= 100) {
            // 11e-100eth = 50k-250k $Drip
            const min = 50000;
            const max = 250000;
            allocation = min + ((gasEth - 10) / 90) * (max - min);
        } else if (gasEth > 100 && gasEth <= 1000) {
            // 101e-1000eth = 250k-750k $Drip
            const min = 250000;
            const max = 750000;
            allocation = min + ((gasEth - 100) / 900) * (max - min);
        } else {
            // For very high gas usage, cap at max tier
            allocation = 750000;
        }

        // Add some randomness within the tier for more realistic feel (only for first calculation)
        const variance = allocation * 0.05; // 5% variance
        const randomOffset = (Math.random() - 0.5) * variance;
        allocation += randomOffset;

        // Ensure minimum allocation (but only if gas was actually spent)
        allocation = Math.max(allocation, 100);

        // Save this allocation permanently for this address
        this.saveAllocation(address, allocation);

        return allocation.toFixed(2);
    }

    // Get saved allocation for an address
    getSavedAllocation(address) {
        if (!address) return null;

        try {
            const saved = localStorage.getItem('dripme_allocations');
            if (!saved) return null;

            const allocations = JSON.parse(saved);
            return allocations[address.toLowerCase()] || null;
        } catch (e) {
            return null;
        }
    }

    // Save allocation for an address (permanent, never changes)
    saveAllocation(address, allocation) {
        if (!address) return;

        try {
            const saved = localStorage.getItem('dripme_allocations');
            const allocations = saved ? JSON.parse(saved) : {};

            // Only save if not already saved (first time only)
            const addressKey = address.toLowerCase();
            if (!allocations[addressKey]) {
                allocations[addressKey] = allocation;
                localStorage.setItem('dripme_allocations', JSON.stringify(allocations));
            }
        } catch (e) {
            // Silently handle save errors
        }
    }
}

(function attachAirdrop() {
    const allocator = new AirdropAllocator();

    async function updateUIForAddress(address) {
        console.log('🔄 updateUIForAddress called with:', address);
        const status = document.getElementById('airdrop-status');
        const stats = document.getElementById('airdrop-stats');
        const allocAmountEl = document.getElementById('allocation-amount');
        if (!status) return;

        status.textContent = 'Calculating your allocation...';

        try {
            const addressParam = address || (window.feesWTFApp && window.feesWTFApp.walletAddress);
            console.log('📍 Using address:', addressParam);

            if (!addressParam) {
                status.textContent = 'Please connect your wallet to check allocation.';
                return;
            }

            let totalGasEth = 0;
            let txCount = 0;

            // First try to get data from localStorage (saved gas data)
            const savedGasData = localStorage.getItem('dripme_gas_data');
            if (savedGasData) {
                try {
                    const gasData = JSON.parse(savedGasData);
                    if (gasData.address && gasData.address.toLowerCase() === addressParam.toLowerCase()) {
                        totalGasEth = gasData.totalGasSpent || 0;
                        txCount = gasData.totalTransactions || 0;
                    }
                } catch (e) {
                    // Silently handle parsing errors
                }
            }

            // If no saved data, fetch from API
            if (totalGasEth === 0) {
                const apiUrl = `${allocator.baseUrl}?module=account&action=txlist&address=${addressParam}&startblock=0&endblock=********&sort=asc&apikey=${allocator.apiKey}`;
                const proxyUrl = `cors-proxy.php?url=${encodeURIComponent(apiUrl)}`;
                const res = await fetch(proxyUrl);
                const data = await res.json();

                let totalGasWei = 0n;

                if (data.status === '1' && data.result && Array.isArray(data.result) && data.result.length > 0) {
                    txCount = data.result.length;
                    for (const tx of data.result) {
                        const gasUsed = BigInt(tx.gasUsed || '0');
                        const gasPrice = BigInt(tx.gasPrice || '0');
                        totalGasWei += gasUsed * gasPrice;
                    }
                    totalGasEth = Number(totalGasWei) / 1e18;
                } else {
                    // No transactions found - set allocation to zero
                    txCount = 0;
                    totalGasEth = 0;
                }
            }

            // Prepare gas data for sharing
            const gasData = {
                totalGasSpent: totalGasEth,
                totalTransactions: txCount,
                address: addressParam
            };

            console.log('📊 Gas data prepared:', gasData);

            // Check if sharing is required before showing allocation
            console.log('🔍 Checking social sharing manager:', !!window.socialSharingManager);
            if (window.socialSharingManager && window.socialSharingManager.requireSharing(gasData)) {
                console.log('🐦 Sharing required - showing sharing interface');

                // Make sure the allocation section is visible
                showAllocationSection();
                if (stats) stats.classList.remove('hidden');

                // The requireSharing method already shows the sharing interface
                // No need to do anything else here
                return;
            }

            console.log('✅ No sharing required - showing allocation');
            // User has already shared or sharing not required - show allocation
            showAllocation(gasData, allocAmountEl, status, stats);

        } catch (e) {
            console.error('Error in updateUIForAddress:', e);

            // Show fallback allocation based on saved data if available
            const savedGasData = localStorage.getItem('dripme_gas_data');
            if (savedGasData && allocAmountEl) {
                try {
                    const gasData = JSON.parse(savedGasData);
                    const fallbackAllocation = allocator.allocationForGas(gasData.totalGasSpent || 0, gasData.address);
                    allocAmountEl.textContent = fallbackAllocation;

                    // Show proper status for fallback data
                    if (gasData.totalGasSpent === 0) {
                        status.innerHTML = `
                            <div style="text-align: center;">
                                <p><strong>No transactions found</strong></p>
                                <p>This wallet has no transaction history on Ethereum</p>
                                <p style="color: #ff6b6b; margin-top: 10px;">Allocation: 0 $DRIP</p>
                            </div>
                        `;
                    } else {
                        status.innerHTML = `
                            <div style="text-align: center;">
                                <p><strong>Ξ${gasData.totalGasSpent.toFixed(3)}</strong> Total Gas Spent</p>
                                <p><strong>$${(gasData.totalGasSpent * 3500).toFixed(2)}</strong> (${gasData.totalTransactions} transactions)</p>
                            </div>
                        `;
                    }
                } catch (e) {
                    allocAmountEl.textContent = '0.00';
                    status.textContent = 'Unable to calculate allocation right now.';
                }
            } else if (allocAmountEl) {
                allocAmountEl.textContent = '0.00';
                status.textContent = 'Unable to calculate allocation right now.';
            }
            if (stats) stats.classList.remove('hidden');
        }
    }

    // Method to show allocation after sharing verification
    function showAllocation(gasData, allocAmountEl, status, stats) {
        const allocation = allocator.allocationForGas(gasData.totalGasSpent, gasData.address);

        if (allocAmountEl) {
            allocAmountEl.textContent = allocation;
        }

        // Update status with gas spent info
        if (gasData.totalGasSpent === 0) {
            status.innerHTML = `
                <div style="text-align: center;">
                    <p><strong>No transactions found</strong></p>
                    <p>This wallet has no transaction history on Ethereum</p>
                    <p style="color: #ff6b6b; margin-top: 10px;">Allocation: 0 $DRIP</p>
                </div>
            `;
        } else {
            status.innerHTML = `
                <div style="text-align: center;">
                    <p><strong>Ξ${gasData.totalGasSpent.toFixed(3)}</strong> Total Gas Spent</p>
                    <p><strong>$${(gasData.totalGasSpent * 3500).toFixed(2)}</strong> (${gasData.totalTransactions} transactions)</p>
                </div>
            `;
        }

        if (stats) stats.classList.remove('hidden');
    }

    // Expose functions globally for social sharing manager
    window.airdropManager = {
        showAllocation: (gasData) => {
            const allocAmountEl = document.getElementById('allocation-amount');
            const status = document.getElementById('airdrop-status');
            const stats = document.getElementById('airdrop-stats');
            showAllocationSection();
            showAllocation(gasData, allocAmountEl, status, stats);
        },
        displayAllocationInBox: (gasData) => {
            // This method displays the allocation directly in the feature card box
            const allocationSection = document.querySelector('.feature-card');
            if (!allocationSection) return;

            const allocation = allocator.allocationForGas(gasData.totalGasSpent, gasData.address);

            const allocationHTML = `
                <div class="allocation-display-final">
                    <div style="text-align: center; padding: 40px 20px;">
                        <div style="margin-bottom: 20px;">
                            <img src="./images/driplogo.PNG" alt="DRIP Token" style="width: 60px; height: 60px; border-radius: 50%;" onerror="this.style.display='none'">
                        </div>
                        <h3 style="color: #ffffff; margin: 0 0 8px 0; font-size: 1.2rem;">Your $DRIP Allocation</h3>
                        <div style="
                            background: linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
                            border: 1px solid rgba(34, 197, 94, 0.3);
                            border-radius: 12px;
                            padding: 24px;
                            margin: 20px 0;
                        ">
                            <div style="font-size: 2.5rem; font-weight: bold; color: #22c55e; margin-bottom: 8px;">
                                ${allocation}
                            </div>
                            <div style="color: #ffffff; font-size: 1.1rem; font-weight: 600;">
                                $DRIP Tokens
                            </div>
                        </div>
                        <p style="margin: 0; font-size: 0.9rem; opacity: 0.7; color: #b0b0b0;">
                            Based on ${gasData.totalGasSpent.toFixed(3)} ETH gas spent
                        </p>
                    </div>
                </div>
            `;

            allocationSection.innerHTML = allocationHTML;
        },
        showAllocationSection: showAllocationSection,
        hideAllocationSection: hideAllocationSection
    };

    // Hook into app wallet connection
    const origOnWalletConnected = window.feesWTFApp && window.feesWTFApp.onWalletConnected?.bind(window.feesWTFApp);
    if (window.feesWTFApp) {
        window.feesWTFApp.onWalletConnected = function(address, provider) {
            if (origOnWalletConnected) origOnWalletConnected(address, provider);

            // Check if this is a different wallet than previously connected
            const lastConnectedWallet = localStorage.getItem('dripme_last_wallet');
            if (lastConnectedWallet && lastConnectedWallet.toLowerCase() !== address.toLowerCase()) {
                // Different wallet - clear gas data for the old wallet
                localStorage.removeItem('dripme_gas_data');
            }

            // Save current wallet as last connected
            localStorage.setItem('dripme_last_wallet', address.toLowerCase());

            updateUIForAddress(address);
        };
    }

    // Hide allocation section by default until sharing is complete
    function hideAllocationSection() {
        const allocationSection = document.querySelector('.feature-card');

        if (allocationSection && allocationSection.querySelector('#allocation-amount')) {
            allocationSection.style.display = 'none';
        }
    }

    // Show allocation section after sharing
    function showAllocationSection() {
        const allocationSection = document.querySelector('.feature-card');
        if (allocationSection && allocationSection.querySelector('#allocation-amount')) {
            allocationSection.style.display = 'block';
        }
    }

    // Check if user has shared for current address
    function hasUserShared(address) {
        const unlockedAddress = localStorage.getItem('dripme_unlocked_address');
        const isUnlocked = localStorage.getItem('dripme_allocation_unlocked') === 'true';
        const result = isUnlocked && unlockedAddress === address?.toLowerCase();
        console.log('🔍 hasUserShared check:', {
            address: address?.toLowerCase(),
            unlockedAddress,
            isUnlocked,
            result
        });
        return result;
    }

    // Function to show sharing required message
    function showSharingRequiredMessage() {
        const status = document.getElementById('airdrop-status');
        if (status) {
            status.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <h3 style="color: #667eea; margin-bottom: 15px;">🐦 Share to Unlock Your Allocation</h3>
                    <p style="margin-bottom: 20px;">Connect your wallet and share your gas tracking results on Twitter to see your $DRIP allocation!</p>
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 15px; border-radius: 10px; color: white;">
                        <p style="margin: 0; font-weight: 500;">Step 1: Connect Wallet → Step 2: Share on Twitter → Step 3: See Allocation</p>
                    </div>
                </div>
            `;
        }
    }

    // Initially hide allocation section
    hideAllocationSection();

    // If already connected, check sharing status
    if (window.feesWTFApp && window.feesWTFApp.walletAddress) {
        if (hasUserShared(window.feesWTFApp.walletAddress)) {
            showAllocationSection();
            updateUIForAddress(window.feesWTFApp.walletAddress);
        } else {
            showSharingRequiredMessage();
        }
    } else {
        // No wallet connected - show sharing requirement
        showSharingRequiredMessage();
    }

    // Also check for saved gas data on page load
    const savedGasData = localStorage.getItem('dripme_gas_data');
    if (savedGasData) {
        try {
            const gasData = JSON.parse(savedGasData);
            if (gasData.address && gasData.totalGasSpent > 0) {
                if (hasUserShared(gasData.address)) {
                    showAllocationSection();
                    updateUIForAddress(gasData.address);
                } else {
                    showSharingRequiredMessage();
                }
            }
        } catch (e) {
            // Silently handle saved data errors
        }
    }

    // Also check for any saved allocations and display if available
    const savedAllocations = localStorage.getItem('dripme_allocations');
    if (savedAllocations && !window.feesWTFApp?.walletAddress) {
        try {
            const allocations = JSON.parse(savedAllocations);
            const addresses = Object.keys(allocations);
            if (addresses.length > 0) {
                // Show the most recent allocation if no wallet is connected
                const lastAddress = addresses[addresses.length - 1];
                const allocAmountEl = document.getElementById('allocation-amount');
                const status = document.getElementById('airdrop-status');
                const stats = document.getElementById('airdrop-stats');

                if (allocAmountEl) {
                    allocAmountEl.textContent = allocations[lastAddress].toFixed(2);
                }
                if (status) {
                    status.innerHTML = `<p>Previous allocation found for ${lastAddress.substring(0, 6)}...${lastAddress.substring(lastAddress.length - 4)}</p>`;
                }
                if (stats) stats.classList.remove('hidden');
            }
        } catch (e) {
            // Silently handle saved allocation errors
        }
    }
})();


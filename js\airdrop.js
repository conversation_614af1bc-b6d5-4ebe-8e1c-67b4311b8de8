class AirdropAllocator {
    constructor() {
        this.apiKey = (window.apiConfig && window.apiConfig.endpoints.etherscan.apiKey) || 'YourEtherscanAPIKey';
        this.baseUrl = 'https://api.etherscan.io/api';
    }

    async fetchGasForAddress(address) {
        const url = `${this.baseUrl}?module=account&action=txlist&address=${address}&startblock=0&endblock=********&sort=asc&apikey=${this.apiKey}`;
        const res = await fetch(url);
        if (!res.ok) throw new Error('Failed to fetch transactions');
        const data = await res.json();
        if (data.status !== '1' || !Array.isArray(data.result)) return { gasUsed: 0, txCount: 0 };

        // Sum gasUsed * gasPrice in wei; also sum gasUsed
        let totalGasUsed = 0n;
        for (const tx of data.result) {
            try {
                const gasUsed = BigInt(tx.gasUsed || '0');
                totalGasUsed += gasUsed; // for tiering we only need gasUsed count
            } catch (_) {}
        }
        return { gasUsed: Number(totalGasUsed), txCount: data.result.length };
    }

    // Tiering per user's spec
    allocationForGas(gasEth) {
        // gasEth is total gas spent measured in ETH equivalent; we only have gasUsed not multiplied by gasPrice here.
        // If only gasUsed is available, we can still tier by ranges. As an approximation,
        // we interpret the provided table as ETH gas spent tiers:
        // 0-1 ETH => 2k-10k
        // 1-10 ETH => 10k-50k
        // 10-100 ETH => 50k-250k
        // 100-1000 ETH => 250k-750k
        // Return string range
        if (gasEth < 1) return '2,000 – 10,000';
        if (gasEth < 10) return '10,000 – 50,000';
        if (gasEth < 100) return '50,000 – 250,000';
        return '250,000 – 750,000';
    }
}

(function attachAirdrop() {
    const allocator = new AirdropAllocator();

    async function updateUIForAddress(address) {
        const status = document.getElementById('airdrop-status');
        const stats = document.getElementById('airdrop-stats');
        const gasEthEl = document.getElementById('gas-spent-eth');
        const allocEl = document.getElementById('allocation-range');
        if (!status) return;
        status.textContent = 'Fetching on-chain activity...';
        try {
            // Prefer Etherscan gasTracker? We compute gas spent by summing gasUsed * gasPrice / 1e18 across txs.
            const addressParam = address || (window.feesWTFApp && window.feesWTFApp.walletAddress);
            const url = `${allocator.baseUrl}?module=account&action=txlist&address=${addressParam}&startblock=0&endblock=********&sort=asc&apikey=${allocator.apiKey}`;
            const res = await fetch(url);
            const data = await res.json();
            let totalGasWei = 0n;
            let totalGasEth = 0;
            if (data.status === '1') {
                for (const tx of data.result) {
                    const gasUsed = BigInt(tx.gasUsed || '0');
                    const gasPrice = BigInt(tx.gasPrice || '0');
                    totalGasWei += gasUsed * gasPrice;
                }
                totalGasEth = Number(totalGasWei) / 1e18;
            }
            const range = allocator.allocationForGas(totalGasEth);
            if (gasEthEl) gasEthEl.textContent = totalGasEth.toFixed(4);
            if (allocEl) allocEl.textContent = `${range} $DRIP`;
            status.textContent = 'Your dynamic allocation based on gas usage:';
            if (stats) stats.classList.remove('hidden');
        } catch (e) {
            status.textContent = 'Unable to fetch activity right now.';
            console.error(e);
        }
    }

    // Hook into app wallet connection
    const origOnWalletConnected = window.feesWTFApp && window.feesWTFApp.onWalletConnected?.bind(window.feesWTFApp);
    if (window.feesWTFApp) {
        window.feesWTFApp.onWalletConnected = function(address, provider) {
            if (origOnWalletConnected) origOnWalletConnected(address, provider);
            updateUIForAddress(address);
        };
    }

    // If already connected
    if (window.feesWTFApp && window.feesWTFApp.walletAddress) {
        updateUIForAddress(window.feesWTFApp.walletAddress);
    }
})();


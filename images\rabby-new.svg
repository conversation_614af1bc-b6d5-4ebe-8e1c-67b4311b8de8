<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" rx="8" fill="url(#rabby-gradient)"/>
  <path d="M8 12C8 8.68629 10.6863 6 14 6H18C21.3137 6 24 8.68629 24 12V20C24 23.3137 21.3137 26 18 26H14C10.6863 26 8 23.3137 8 20V12Z" fill="white"/>
  <!-- Rabbit ears -->
  <ellipse cx="13" cy="8" rx="2" ry="4" fill="url(#rabby-ear)" transform="rotate(-15 13 8)"/>
  <ellipse cx="19" cy="8" rx="2" ry="4" fill="url(#rabby-ear)" transform="rotate(15 19 8)"/>
  <!-- Face -->
  <circle cx="16" cy="16" r="6" fill="url(#rabby-face)"/>
  <!-- Eyes -->
  <circle cx="14" cy="14" r="1" fill="#1A1A1A"/>
  <circle cx="18" cy="14" r="1" fill="#1A1A1A"/>
  <!-- Nose -->
  <path d="M16 16L15 17L17 17Z" fill="#FF6B9D"/>
  <!-- Mouth -->
  <path d="M14 19C14 19 15 20 16 20C17 20 18 19 18 19" stroke="#1A1A1A" stroke-width="1" stroke-linecap="round" fill="none"/>
  <defs>
    <linearGradient id="rabby-gradient" x1="0" y1="0" x2="32" y2="32" gradientUnits="userSpaceOnUse">
      <stop stop-color="#7C3AED"/>
      <stop offset="1" stop-color="#EC4899"/>
    </linearGradient>
    <linearGradient id="rabby-ear" x1="0" y1="0" x2="4" y2="8" gradientUnits="userSpaceOnUse">
      <stop stop-color="#DDD6FE"/>
      <stop offset="1" stop-color="#C084FC"/>
    </linearGradient>
    <linearGradient id="rabby-face" x1="10" y1="10" x2="22" y2="22" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F3E8FF"/>
      <stop offset="1" stop-color="#DDD6FE"/>
    </linearGradient>
  </defs>
</svg>

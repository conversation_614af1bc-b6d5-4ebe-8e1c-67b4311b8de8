<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" rx="12" fill="url(#opensea-bg)"/>
  <!-- Ship sail -->
  <path d="M32 8L48 32L32 28L16 32L32 8Z" fill="url(#sail)"/>
  <!-- Ship hull -->
  <path d="M16 32L48 32L44 48L20 48L16 32Z" fill="url(#hull)"/>
  <!-- Mast -->
  <rect x="31" y="8" width="2" height="40" fill="url(#mast)"/>
  <!-- Waves -->
  <path d="M8 48C12 44 16 52 20 48C24 44 28 52 32 48C36 44 40 52 44 48C48 44 52 52 56 48" 
        stroke="url(#waves)" stroke-width="2" fill="none"/>
  <path d="M8 52C12 48 16 56 20 52C24 48 28 56 32 52C36 48 40 56 44 52C48 48 52 56 56 52" 
        stroke="url(#waves)" stroke-width="2" fill="none"/>
  <!-- OpenSea logo elements -->
  <circle cx="32" cy="24" r="4" fill="white" opacity="0.8"/>
  <defs>
    <linearGradient id="opensea-bg" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#2081E2"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="sail" x1="16" y1="8" x2="48" y2="32" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFFFF"/>
      <stop offset="1" stop-color="#F3F4F6"/>
    </linearGradient>
    <linearGradient id="hull" x1="16" y1="32" x2="48" y2="48" gradientUnits="userSpaceOnUse">
      <stop stop-color="#92400E"/>
      <stop offset="1" stop-color="#78350F"/>
    </linearGradient>
    <linearGradient id="mast" x1="31" y1="8" x2="33" y2="48" gradientUnits="userSpaceOnUse">
      <stop stop-color="#78350F"/>
      <stop offset="1" stop-color="#451A03"/>
    </linearGradient>
    <linearGradient id="waves" x1="8" y1="48" x2="56" y2="56" gradientUnits="userSpaceOnUse">
      <stop stop-color="#60A5FA"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>
  </defs>
</svg>

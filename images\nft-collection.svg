<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" rx="12" fill="url(#nft-bg)"/>
  <!-- Main NFT frame -->
  <rect x="8" y="8" width="48" height="48" rx="8" fill="white" stroke="url(#nft-border)" stroke-width="2"/>
  <!-- Inner artwork -->
  <rect x="12" y="12" width="40" height="32" rx="4" fill="url(#nft-artwork)"/>
  <!-- NFT text area -->
  <rect x="12" y="46" width="40" height="6" rx="2" fill="url(#nft-text-bg)"/>
  <!-- Decorative elements -->
  <circle cx="20" cy="20" r="3" fill="url(#nft-accent-1)"/>
  <circle cx="44" cy="20" r="3" fill="url(#nft-accent-2)"/>
  <rect x="26" y="24" width="12" height="12" rx="2" fill="url(#nft-center)"/>
  <!-- WTF text -->
  <text x="32" y="51" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="4" font-weight="bold">WTF</text>
  <defs>
    <linearGradient id="nft-bg" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#1E293B"/>
      <stop offset="1" stop-color="#0F172A"/>
    </linearGradient>
    <linearGradient id="nft-border" x1="8" y1="8" x2="56" y2="56" gradientUnits="userSpaceOnUse">
      <stop stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#1D4ED8"/>
    </linearGradient>
    <linearGradient id="nft-artwork" x1="12" y1="12" x2="52" y2="44" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F59E0B"/>
      <stop offset="0.5" stop-color="#EF4444"/>
      <stop offset="1" stop-color="#8B5CF6"/>
    </linearGradient>
    <linearGradient id="nft-text-bg" x1="12" y1="46" x2="52" y2="52" gradientUnits="userSpaceOnUse">
      <stop stop-color="#374151"/>
      <stop offset="1" stop-color="#1F2937"/>
    </linearGradient>
    <radialGradient id="nft-accent-1" cx="20" cy="20" r="3" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FBBF24"/>
      <stop offset="1" stop-color="#F59E0B"/>
    </radialGradient>
    <radialGradient id="nft-accent-2" cx="44" cy="20" r="3" gradientUnits="userSpaceOnUse">
      <stop stop-color="#34D399"/>
      <stop offset="1" stop-color="#10B981"/>
    </radialGradient>
    <linearGradient id="nft-center" x1="26" y1="24" x2="38" y2="36" gradientUnits="userSpaceOnUse">
      <stop stop-color="#A78BFA"/>
      <stop offset="1" stop-color="#7C3AED"/>
    </linearGradient>
  </defs>
</svg>

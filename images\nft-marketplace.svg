<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" rx="12" fill="url(#marketplace-bg)"/>
  <!-- Store front -->
  <rect x="8" y="20" width="48" height="36" rx="4" fill="url(#store-front)"/>
  <!-- Roof -->
  <path d="M4 20L32 8L60 20L56 16L32 4L8 16L4 20Z" fill="url(#store-roof)"/>
  <!-- Windows/displays -->
  <rect x="12" y="24" width="16" height="12" rx="2" fill="url(#display-1)"/>
  <rect x="36" y="24" width="16" height="12" rx="2" fill="url(#display-2)"/>
  <!-- Door -->
  <rect x="28" y="40" width="8" height="16" rx="2" fill="url(#store-door)"/>
  <!-- Door handle -->
  <circle cx="34" cy="48" r="1" fill="#FBBF24"/>
  <!-- NFT items in windows -->
  <rect x="14" y="26" width="4" height="4" rx="1" fill="#F59E0B"/>
  <rect x="20" y="26" width="4" height="4" rx="1" fill="#EF4444"/>
  <rect x="14" y="30" width="4" height="4" rx="1" fill="#8B5CF6"/>
  <rect x="20" y="30" width="4" height="4" rx="1" fill="#10B981"/>
  
  <rect x="38" y="26" width="4" height="4" rx="1" fill="#3B82F6"/>
  <rect x="44" y="26" width="4" height="4" rx="1" fill="#F59E0B"/>
  <rect x="38" y="30" width="4" height="4" rx="1" fill="#EF4444"/>
  <rect x="44" y="30" width="4" height="4" rx="1" fill="#8B5CF6"/>
  
  <defs>
    <linearGradient id="marketplace-bg" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#1E40AF"/>
      <stop offset="1" stop-color="#1E3A8A"/>
    </linearGradient>
    <linearGradient id="store-front" x1="8" y1="20" x2="56" y2="56" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F3F4F6"/>
      <stop offset="1" stop-color="#E5E7EB"/>
    </linearGradient>
    <linearGradient id="store-roof" x1="4" y1="4" x2="60" y2="20" gradientUnits="userSpaceOnUse">
      <stop stop-color="#7C2D12"/>
      <stop offset="1" stop-color="#991B1B"/>
    </linearGradient>
    <linearGradient id="display-1" x1="12" y1="24" x2="28" y2="36" gradientUnits="userSpaceOnUse">
      <stop stop-color="#DBEAFE"/>
      <stop offset="1" stop-color="#BFDBFE"/>
    </linearGradient>
    <linearGradient id="display-2" x1="36" y1="24" x2="52" y2="36" gradientUnits="userSpaceOnUse">
      <stop stop-color="#DBEAFE"/>
      <stop offset="1" stop-color="#BFDBFE"/>
    </linearGradient>
    <linearGradient id="store-door" x1="28" y1="40" x2="36" y2="56" gradientUnits="userSpaceOnUse">
      <stop stop-color="#92400E"/>
      <stop offset="1" stop-color="#78350F"/>
    </linearGradient>
  </defs>
</svg>

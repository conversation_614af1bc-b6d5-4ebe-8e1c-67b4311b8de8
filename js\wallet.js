class WalletManager {
    constructor() {
        this.isConnected = false;
        this.currentWallet = null;
        this.address = null;
        this.provider = null;
        this.web3 = null;
        
        this.supportedWallets = {
            metamask: 'MetaMask',
            phantom: 'Phantom',
            walletconnect: 'WalletConnect',
            coinbase: 'Coinbase Wallet'
        };
        
        this.init();
    }

    init() {
        // Check for persisted wallet connection
        this.loadPersistedConnection();
        this.checkExistingConnections();
    }

    loadPersistedConnection() {
        try {
            const persistedWallet = localStorage.getItem('drip_wallet_connection');
            if (persistedWallet) {
                const walletData = JSON.parse(persistedWallet);
                console.log('🔄 Found persisted wallet connection:', walletData.type);

                // Set basic connection state
                this.currentWallet = walletData.type;
                this.address = walletData.address;

                // The actual connection will be verified in checkExistingConnections
            }
        } catch (error) {
            console.warn('Failed to load persisted wallet connection:', error);
            localStorage.removeItem('drip_wallet_connection');
        }
    }

    saveWalletConnection() {
        if (this.isConnected && this.currentWallet && this.address) {
            const walletData = {
                type: this.currentWallet,
                address: this.address,
                timestamp: Date.now()
            };
            localStorage.setItem('drip_wallet_connection', JSON.stringify(walletData));
            console.log('💾 Wallet connection saved to localStorage');
        }
    }

    clearPersistedConnection() {
        localStorage.removeItem('drip_wallet_connection');
        console.log('🗑️ Cleared persisted wallet connection');
    }

    async checkExistingConnections() {
        if (window.ethereum && window.ethereum.isMetaMask) {
            try {
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                if (accounts.length > 0) {
                    await this.connectMetaMask(false);
                }
            } catch (error) {
                console.log('No existing MetaMask connection');
            }
        }

        if (window.solana && window.solana.isPhantom) {
            try {
                const response = await window.solana.connect({ onlyIfTrusted: true });
                if (response.publicKey) {
                    await this.connectPhantom(false);
                }
            } catch (error) {
                console.log('No existing Phantom connection');
            }
        }
    }

    async connect(walletType) {
        try {
            switch (walletType) {
                case 'metamask':
                    return await this.connectMetaMask();
                case 'phantom':
                    return await this.connectPhantom();
                case 'walletconnect':
                    return await this.connectWalletConnect();
                case 'coinbase':
                    return await this.connectCoinbase();
                default:
                    throw new Error(`Unsupported wallet type: ${walletType}`);
            }
        } catch (error) {
            console.error(`Error connecting to ${walletType}:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async connectMetaMask(requestAccounts = true) {
        if (!window.ethereum) {
            throw new Error('MetaMask is not installed. Please install MetaMask from https://metamask.io to continue.');
        }

        if (!window.ethereum.isMetaMask) {
            throw new Error('Please use MetaMask browser extension or mobile app.');
        }

        try {
            let accounts;
            if (requestAccounts) {
                accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
            } else {
                accounts = await window.ethereum.request({ method: 'eth_accounts' });
            }

            if (accounts.length === 0) {
                throw new Error('No accounts found. Please connect your MetaMask wallet.');
            }

            this.address = accounts[0];
            this.provider = window.ethereum;
            this.currentWallet = 'metamask';
            this.isConnected = true;

            // Save connection to localStorage
            this.saveWalletConnection();

            this.setupMetaMaskListeners();

            console.log('✅ MetaMask connected:', this.address);

            return {
                success: true,
                address: this.address,
                provider: this.provider
            };

        } catch (error) {
            if (error.code === 4001) {
                throw new Error('Connection request was rejected by user');
            } else if (error.code === -32002) {
                throw new Error('MetaMask is already processing a connection request. Please check MetaMask.');
            }
            throw new Error(`Failed to connect MetaMask: ${error.message}`);
        }
    }

    async connectPhantom(requestConnection = true) {
        if (!window.solana) {
            throw new Error('Phantom wallet is not installed. Please install Phantom from https://phantom.app to continue.');
        }

        if (!window.solana.isPhantom) {
            throw new Error('Please use the official Phantom wallet extension or mobile app.');
        }

        try {
            let response;
            if (requestConnection) {
                response = await window.solana.connect();
            } else {
                response = await window.solana.connect({ onlyIfTrusted: true });
            }

            if (!response.publicKey) {
                throw new Error('No accounts found. Please connect your Phantom wallet.');
            }

            this.address = response.publicKey.toString();
            this.provider = window.solana;
            this.currentWallet = 'phantom';
            this.isConnected = true;

            // Save connection to localStorage
            this.saveWalletConnection();

            this.setupPhantomListeners();

            console.log('✅ Phantom connected:', this.address);

            return {
                success: true,
                address: this.address,
                provider: this.provider
            };

        } catch (error) {
            if (error.code === 4001) {
                throw new Error('Connection request was rejected by user');
            } else if (error.message && error.message.includes('User rejected')) {
                throw new Error('Connection request was rejected by user');
            }
            throw new Error(`Failed to connect Phantom: ${error.message}`);
        }
    }

    setupPhantomListeners() {
        if (!window.solana) return;

        window.solana.on('connect', (publicKey) => {
            console.log('Phantom connected:', publicKey.toString());
            this.address = publicKey.toString();
            if (window.feesWTFApp) {
                window.feesWTFApp.onWalletConnected(this.address, this.provider);
            }
        });

        window.solana.on('disconnect', () => {
            console.log('Phantom disconnected');
            this.disconnect();
        });

        window.solana.on('accountChanged', (publicKey) => {
            if (publicKey) {
                console.log('Phantom account changed:', publicKey.toString());
                this.address = publicKey.toString();
                if (window.feesWTFApp) {
                    window.feesWTFApp.onWalletConnected(this.address, this.provider);
                }
            } else {
                this.disconnect();
            }
        });
    }

    async connectWalletConnect() {

        return {
            success: false,
            error: 'WalletConnect integration coming soon'
        };
    }

    async connectCoinbase() {
        
        console.warn('Coinbase Wallet integration not implemented yet');
        
        return {
            success: false,
            error: 'Coinbase Wallet integration coming soon'
        };
    }

    setupMetaMaskListeners() {
        if (!window.ethereum) return;

        window.ethereum.on('accountsChanged', (accounts) => {
            if (accounts.length === 0) {
                this.disconnect();
            } else {
                this.address = accounts[0];
                if (window.feesWTFApp) {
                    window.feesWTFApp.onWalletConnected(this.address, this.provider);
                }
            }
        });

        window.ethereum.on('chainChanged', (chainId) => {
            console.log('Chain changed to:', chainId);
            window.location.reload();
        });

        window.ethereum.on('disconnect', () => {
            this.disconnect();
        });
    }

    disconnect() {
        this.isConnected = false;
        this.currentWallet = null;
        this.address = null;
        this.provider = null;
        this.web3 = null;

        // Clear persisted connection
        this.clearPersistedConnection();

        console.log('👋 Wallet disconnected');
    }

    getWalletAvailability() {
        return {
            metamask: {
                available: !!(window.ethereum && window.ethereum.isMetaMask),
                installUrl: 'https://metamask.io/download/'
            },
            phantom: {
                available: !!(window.solana && window.solana.isPhantom),
                installUrl: 'https://phantom.app/download'
            }
        };
    }

    async checkConnection() {
        return {
            isConnected: this.isConnected,
            address: this.address,
            provider: this.provider,
            walletType: this.currentWallet
        };
    }

    async getBalance(address = null) {
        if (!this.provider) {
            throw new Error('No wallet connected');
        }

        const targetAddress = address || this.address;
        if (!targetAddress) {
            throw new Error('No address provided');
        }

        try {
            const balance = await this.provider.request({
                method: 'eth_getBalance',
                params: [targetAddress, 'latest']
            });

            const balanceInETH = parseInt(balance, 16) / Math.pow(10, 18);
            return balanceInETH;

        } catch (error) {
            console.error('Error getting balance:', error);
            throw new Error('Failed to get wallet balance');
        }
    }

    async getNetwork() {
        if (!this.provider) {
            throw new Error('No wallet connected');
        }

        try {
            const chainId = await this.provider.request({ method: 'eth_chainId' });
            return {
                chainId: parseInt(chainId, 16),
                name: this.getNetworkName(parseInt(chainId, 16))
            };
        } catch (error) {
            console.error('Error getting network:', error);
            throw new Error('Failed to get network information');
        }
    }

    getNetworkName(chainId) {
        const networks = {
            1: 'Ethereum Mainnet',
            3: 'Ropsten Testnet',
            4: 'Rinkeby Testnet',
            5: 'Goerli Testnet',
            42: 'Kovan Testnet',
            137: 'Polygon Mainnet',
            80001: 'Polygon Mumbai Testnet',
            56: 'BSC Mainnet',
            97: 'BSC Testnet'
        };

        return networks[chainId] || `Unknown Network (${chainId})`;
    }

    async switchToEthereum() {
        if (!this.provider) {
            throw new Error('No wallet connected');
        }

        try {
            await this.provider.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: '0x1' }] 
            });
        } catch (error) {
            console.error('Error switching to Ethereum:', error);
            throw new Error('Failed to switch to Ethereum network');
        }
    }

    async sendTransaction(transaction) {
        if (!this.provider || !this.address) {
            throw new Error('No wallet connected');
        }

        try {
            const txHash = await this.provider.request({
                method: 'eth_sendTransaction',
                params: [{
                    from: this.address,
                    ...transaction
                }]
            });

            return txHash;
        } catch (error) {
            console.error('Error sending transaction:', error);
            throw new Error('Transaction failed');
        }
    }

    async signMessage(message) {
        if (!this.provider || !this.address) {
            throw new Error('No wallet connected');
        }

        try {
            const signature = await this.provider.request({
                method: 'personal_sign',
                params: [message, this.address]
            });

            return signature;
        } catch (error) {
            console.error('Error signing message:', error);
            throw new Error('Message signing failed');
        }
    }

    isWalletInstalled(walletType) {
        switch (walletType) {
            case 'metamask':
                return !!(window.ethereum && window.ethereum.isMetaMask);
            case 'coinbase':
                return !!(window.ethereum && window.ethereum.isCoinbaseWallet);
            default:
                return false;
        }
    }

    getWalletDownloadUrl(walletType) {
        const urls = {
            metamask: 'https://metamask.io/download/',
            coinbase: 'https://wallet.coinbase.com/',
            walletconnect: 'https://walletconnect.com/'
        };

        return urls[walletType] || '#';
    }
}

window.walletManager = new WalletManager();

<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" rx="12" fill="url(#common-bg)"/>
  <!-- Simple hexagon -->
  <path d="M32 12L44 20V36L32 44L20 36V20L32 12Z" fill="url(#common-hex)"/>
  <!-- Inner hexagon -->
  <path d="M32 20L36 24V32L32 36L28 32V24L32 20Z" fill="url(#common-inner)"/>
  <!-- Center dot -->
  <circle cx="32" cy="28" r="3" fill="url(#common-center)"/>
  <defs>
    <linearGradient id="common-bg" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#374151"/>
      <stop offset="1" stop-color="#1F2937"/>
    </linearGradient>
    <linearGradient id="common-hex" x1="20" y1="12" x2="44" y2="44" gradientUnits="userSpaceOnUse">
      <stop stop-color="#9CA3AF"/>
      <stop offset="1" stop-color="#6B7280"/>
    </linearGradient>
    <linearGradient id="common-inner" x1="28" y1="20" x2="36" y2="36" gradientUnits="userSpaceOnUse">
      <stop stop-color="#D1D5DB"/>
      <stop offset="1" stop-color="#9CA3AF"/>
    </linearGradient>
    <radialGradient id="common-center" cx="32" cy="28" r="3" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFFFF"/>
      <stop offset="1" stop-color="#D1D5DB"/>
    </radialGradient>
  </defs>
</svg>

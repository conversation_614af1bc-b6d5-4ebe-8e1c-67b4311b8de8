/* Landing Page Styles */

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(-60px) rotate(240deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
}

.hero-text {
    max-width: 600px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.gradient-text {
    background: linear-gradient(135deg, var(--text-accent), #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(90deg); }
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button.primary {
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta-button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.cta-button.secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.cta-button.secondary:hover {
    border-color: var(--text-accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.button-icon {
    font-size: 1.25rem;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.gas-preview-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: float-card 6s ease-in-out infinite;
}

@keyframes float-card {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.preview-icon {
    font-size: 1.5rem;
}

.preview-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.preview-prices {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.preview-price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border-radius: 0.5rem;
}

.price-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.price-value {
    font-weight: 600;
    color: var(--text-primary);
}

.preview-eth-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    border-radius: 0.5rem;
    color: white;
}

.eth-label {
    font-size: 0.875rem;
}

.eth-value {
    font-weight: 700;
    font-size: 1.125rem;
}

/* Section Headers */
.section-header-center {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.subsection-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: var(--text-primary);
    text-align: center;
}

/* Gas Tracker Section */
.gas-tracker-section {
    padding: 6rem 0;
    background: var(--bg-primary);
}

/* Analytics Section */
.analytics-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.analytics-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.analytics-text {
    max-width: 500px;
}

.analytics-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border-radius: 0.5rem;
}

.feature-icon {
    font-size: 1.25rem;
}

.feature-text {
    font-weight: 500;
    color: var(--text-primary);
}

.analytics-input {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.address-input {
    flex: 1;
    min-width: 250px;
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.address-input:focus {
    outline: none;
    border-color: var(--text-accent);
    box-shadow: 0 0 0 3px rgba(var(--text-accent-rgb), 0.1);
}

.address-input::placeholder {
    color: var(--text-secondary);
}

.track-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    color: white;
    border: none;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.track-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.connect-wallet-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.25rem 2rem;
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    color: white;
    border: none;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    width: 100%;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.connect-wallet-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.connect-note {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.5;
}

/* Stats Preview */
.stats-preview {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.stats-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.stats-badge {
    padding: 0.25rem 0.75rem;
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    color: white;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item.connected {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(78, 205, 196, 0.05) 100%);
    border-color: var(--accent-color);
    box-shadow: 0 2px 8px rgba(78, 205, 196, 0.1);
}

.stat-item.connected .stat-icon {
    color: var(--accent-color);
}

.stat-item.connected .stat-label {
    color: var(--accent-color);
    font-weight: 600;
}

.stat-item .stat-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.stat-secondary {
    font-size: 0.75rem;
    color: var(--text-accent);
    font-weight: 500;
}

.address-display {
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

.address-display.connected {
    color: var(--accent-color);
    background: rgba(78, 205, 196, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid rgba(78, 205, 196, 0.2);
}

/* Features Section */
.features-section {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    position: relative;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2.5rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    border-color: var(--text-accent);
}

.feature-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    border-radius: 50%;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
}

.feature-icon-wrapper .feature-icon {
    font-size: 2rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.feature-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    color: white;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Treasury Section */
.treasury-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.treasury-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.treasury-text {
    max-width: 500px;
}

.treasury-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.treasury-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-primary);
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.treasury-feature:hover {
    transform: translateX(5px);
    border-color: var(--text-accent);
}

.treasury-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.treasury-feature-content h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.treasury-feature-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.treasury-visual {
    display: flex;
    justify-content: center;
}

.treasury-stats-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.treasury-stats-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: var(--text-primary);
    text-align: center;
}

.treasury-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.treasury-stat {
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
}

.treasury-stat .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-accent);
    margin-bottom: 0.5rem;
}

.treasury-stat .stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.treasury-stat .stat-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Tokenomics Section */
.tokenomics-section {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.tokenomics-overview {
    margin-bottom: 4rem;
}

.token-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.token-info-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.token-info-card:hover {
    transform: translateY(-2px);
    border-color: var(--text-accent);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.token-info-card .info-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.info-content {
    flex: 1;
}

.info-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* Distribution Section */
.distribution-section {
    margin-bottom: 4rem;
}

.distribution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.distribution-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.distribution-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.distribution-card:hover::before {
    left: 100%;
}

.distribution-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: var(--text-accent);
}

.distribution-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.distribution-icon {
    font-size: 1.5rem;
}

.distribution-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.distribution-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.distribution-percentage {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-accent);
    margin-bottom: 1rem;
}

.distribution-bar {
    width: 100%;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.distribution-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    border-radius: 3px;
    transition: width 1s ease;
    position: relative;
}

.distribution-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Fees Section */
.fees-section {
    margin-top: 4rem;
}

.fees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.fee-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.fee-card:hover {
    transform: translateY(-3px);
    border-color: var(--text-accent);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.fee-percentage {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-accent);
    margin-bottom: 0.75rem;
}

.fee-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.fee-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Loading and Error States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 3rem;
    color: var(--text-secondary);
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--text-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 0.75rem;
    color: var(--error);
    font-size: 0.875rem;
    margin: 2rem 0;
}

.error-icon {
    font-size: 1.25rem;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .analytics-content,
    .treasury-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 80vh;
        padding: 2rem 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-actions {
        justify-content: center;
    }

    .cta-button {
        padding: 0.875rem 1.5rem;
        font-size: 0.875rem;
    }

    .gas-preview-card {
        padding: 1.5rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .feature-card {
        padding: 2rem 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .analytics-input {
        flex-direction: column;
    }

    .address-input {
        min-width: unset;
    }

    .track-btn {
        width: 100%;
        justify-content: center;
    }

    .token-info-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .distribution-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .fees-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .treasury-features {
        gap: 1rem;
    }

    .treasury-feature {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        width: 100%;
    }

    .cta-button {
        width: 100%;
        justify-content: center;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .gas-prices-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .token-info-grid,
    .distribution-grid,
    .fees-grid {
        grid-template-columns: 1fr;
    }

    .feature-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .feature-icon-wrapper .feature-icon {
        font-size: 1.5rem;
    }

    .stats-preview {
        padding: 1.5rem;
    }

    .treasury-stats-card {
        padding: 1.5rem;
    }

    .distribution-card,
    .fee-card {
        padding: 1.5rem 1rem;
    }
}

/* User Gas Tracking Section */
.user-gas-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.address-input-container {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.address-input {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 300px;
    transition: border-color 0.2s ease;
}

.address-input:focus {
    outline: none;
    border-color: var(--text-accent);
}

.address-input::placeholder {
    color: var(--text-secondary);
}

.track-btn {
    padding: 0.75rem 1.5rem;
    background-color: var(--text-accent);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.track-btn:hover {
    background-color: var(--text-accent-hover);
    transform: translateY(-1px);
}

.gas-stats-container {
    margin-bottom: 2rem;
}

.gas-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gas-stat-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.gas-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-icon {
    font-size: 1.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-secondary {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.address-display {
    font-size: 1.25rem !important;
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 0.5rem;
    color: var(--error);
    font-size: 0.875rem;
}

.error-icon {
    font-size: 1.25rem;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    color: var(--text-secondary);
}

/* WTF Info Section */
.wtf-info-section {
    margin-bottom: 3rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
}

.wtf-header {
    text-align: center;
    margin-bottom: 2rem;
}

.wtf-header h2 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.wtf-header p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
}

.wtf-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
    transition: transform 0.2s ease;
}

.benefit-item:hover {
    transform: translateY(-2px);
}

.benefit-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.benefit-content h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.benefit-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
}

/* Treasury Section */
.treasury-section {
    margin-bottom: 3rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
}

.treasury-header {
    text-align: center;
    margin-bottom: 2rem;
}

.treasury-header h2 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.treasury-header p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
}

.treasury-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.treasury-stat {
    text-align: center;
    padding: 1.5rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
}

.treasury-stat h3 {
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.treasury-stat .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-accent);
}

/* Tokenomics Section */
.tokenomics-section {
    margin-bottom: 3rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
}

.tokenomics-header {
    margin-bottom: 2rem;
}

.tokenomics-header h2 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    text-align: center;
}

.token-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.token-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
}

.token-detail .label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.token-detail .value {
    color: var(--text-primary);
    font-weight: 600;
}

.distribution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.distribution-item {
    text-align: center;
    padding: 1.5rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.distribution-item:hover {
    border-color: var(--text-accent);
    transform: translateY(-2px);
}

.distribution-item h3 {
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.distribution-item .amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.distribution-item .percentage {
    font-size: 0.875rem;
    color: var(--text-accent);
    font-weight: 600;
}

.transfer-fees {
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.transfer-fees h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-align: center;
}

.fee-breakdown {
    display: grid;
    gap: 0.75rem;
}

.fee-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: var(--bg-secondary);
    border-radius: 0.375rem;
}

.fee-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.fee-value {
    color: var(--text-accent);
    font-weight: 600;
    font-size: 0.875rem;
}

/* Chart Section */
.chart-section {
    margin-bottom: 3rem;
}

.chart-section h2 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.chart-container {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    height: 400px;
    position: relative;
}

.chart-container canvas {
    max-height: 100%;
}

/* Tools Section */
.tools-section {
    margin-bottom: 3rem;
}

.tools-section h2 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.tool-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.tool-card:hover::before {
    left: 100%;
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--text-accent);
}

.tool-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.tool-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.tool-card p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Gas Price Indicators */
.gas-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.gas-indicator.low {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.gas-indicator.medium {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.gas-indicator.high {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

/* Network Status */
.network-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success);
    animation: pulse 2s infinite;
}

.status-dot.warning {
    background-color: var(--warning);
}

.status-dot.error {
    background-color: var(--error);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.quick-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.quick-action:hover {
    background-color: var(--bg-tertiary);
    transform: translateY(-1px);
}

.quick-action-icon {
    font-size: 1rem;
}

/* Recent Transactions */
.recent-transactions {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.recent-transactions h3 {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.transaction-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.transaction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.transaction-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.transaction-icon.send {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.transaction-icon.receive {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.transaction-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.transaction-type {
    font-weight: 500;
    color: var(--text-primary);
}

.transaction-time {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.transaction-amount {
    font-weight: 600;
    color: var(--text-primary);
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--text-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Dashboard */
@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .tool-card {
        padding: 1.5rem;
    }

    .chart-container {
        height: 300px;
        padding: 1rem;
    }

    .network-status {
        flex-direction: column;
        gap: 0.5rem;
    }

    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    /* User Gas Tracking Responsive */
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .address-input-container {
        width: 100%;
        flex-direction: column;
        gap: 0.75rem;
    }

    .address-input {
        min-width: unset;
        width: 100%;
    }

    .track-btn {
        width: 100%;
    }

    .gas-stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .gas-stat-card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .address-display {
        font-size: 1rem !important;
    }

    /* WTF Info Responsive */
    .wtf-benefits {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .benefit-item {
        padding: 1rem;
    }

    /* Treasury Responsive */
    .treasury-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .treasury-stat {
        padding: 1rem;
    }

    /* Tokenomics Responsive */
    .token-info {
        grid-template-columns: 1fr;
    }

    .distribution-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .distribution-item {
        padding: 1rem;
    }

    .transfer-fees {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .tool-card {
        padding: 1rem;
    }

    .tool-icon {
        font-size: 2.5rem;
    }

    .chart-container {
        height: 250px;
        padding: 0.75rem;
    }

    /* Small screen adjustments */
    .section-header h2 {
        font-size: 1.5rem;
    }

    .gas-stat-card {
        padding: 0.75rem;
    }

    .stat-value {
        font-size: 1.25rem;
    }

    .distribution-grid {
        grid-template-columns: 1fr;
    }

    .wtf-info-section,
    .treasury-section,
    .tokenomics-section {
        padding: 1rem;
    }

    .benefit-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .benefit-icon {
        font-size: 1.5rem;
    }
}

/* ETH Price Card Styling */
.eth-price-card {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border: none;
    box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);
    position: relative;
    overflow: hidden;
}

.eth-price-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    pointer-events: none;
}

.eth-price-card .gas-card-content {
    position: relative;
    z-index: 1;
}

.eth-price-card .gas-header {
    color: rgba(255, 255, 255, 0.9);
}

.eth-price-card .gas-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
}

.eth-price-card .gas-price {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.5rem;
}

.eth-price-card .gas-breakdown {
    color: rgba(255, 255, 255, 0.8);
}

.eth-price-card .gas-cost {
    color: #ffffff;
    font-weight: 600;
}

.eth-price-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(245, 158, 11, 0.4);
}

/* Mobile Responsiveness Improvements */
@media (max-width: 768px) {
    /* Dashboard Layout */
    .dashboard-container {
        padding: 1rem;
    }

    .dashboard-header {
        padding: 1rem 0;
        margin-bottom: 1.5rem;
    }

    .dashboard-title {
        font-size: 1.75rem;
    }

    /* Transaction Cards */
    .transaction-card {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .transaction-hash {
        font-size: 0.75rem;
    }

    .transaction-amount {
        font-size: 0.875rem;
    }

    .transaction-gas {
        font-size: 0.75rem;
    }

    /* Allocation Display */
    .allocation-display {
        padding: 1.5rem;
    }

    .allocation-icon .token-icon {
        width: 48px;
        height: 48px;
    }

    .allocation-amount {
        font-size: 2.25rem !important;
    }

    .allocation-token {
        font-size: 1rem !important;
    }

    /* Form Elements */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-input {
        padding: 0.75rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .form-label {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    /* Buttons */
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
        min-height: 44px;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1rem;
        min-height: 48px;
    }

    /* ETH Price Card Mobile */
    .eth-price-card .gas-price {
        font-size: 1.25rem;
    }

    .eth-price-card .gas-label {
        font-size: 0.875rem;
    }

    .eth-price-card .gas-breakdown,
    .eth-price-card .gas-cost {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    /* Smaller Mobile Adjustments */
    .dashboard-container {
        padding: 0.75rem;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    .transaction-card {
        padding: 0.875rem;
    }

    .allocation-display {
        padding: 1.25rem;
    }

    .allocation-icon .token-icon {
        width: 40px;
        height: 40px;
    }

    .allocation-amount {
        font-size: 2rem !important;
    }

    .allocation-token {
        font-size: 0.875rem !important;
    }

    .form-input {
        padding: 0.625rem;
    }

    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.8rem;
    }

    .btn-large {
        padding: 0.875rem 1.75rem;
        font-size: 0.9rem;
    }

    /* ETH Price Card Small Mobile */
    .eth-price-card .gas-price {
        font-size: 1.125rem;
    }
}

@media (max-width: 360px) {
    /* Extra Small Mobile */
    .dashboard-container {
        padding: 0.5rem;
    }

    .dashboard-title {
        font-size: 1.375rem;
    }

    .transaction-card {
        padding: 0.75rem;
    }

    .allocation-display {
        padding: 1rem;
    }

    .allocation-amount {
        font-size: 1.75rem !important;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }

    .btn-large {
        padding: 0.75rem 1.5rem;
        font-size: 0.85rem;
    }
}

class ProductionUtils {
    constructor() {
        this.errorLog = [];
        this.performanceMetrics = new Map();
        this.retryConfig = {
            maxRetries: 3,
            baseDelay: 1000,
            maxDelay: 10000
        };
    }

    async handleError(error, context = '', showToUser = true) {
        const errorInfo = {
            message: error.message,
            stack: error.stack,
            context,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.errorLog.push(errorInfo);
        console.error(`[${context}] Error:`, error);

        this.sendErrorToTracking(errorInfo);

        if (showToUser) {
            this.showUserError(this.getUserFriendlyMessage(error, context));
        }

        return errorInfo;
    }

    getUserFriendlyMessage(error, context) {
        const messages = {
            'network': 'Network connection issue. Please check your internet connection.',
            'wallet': 'Wallet connection failed. Please try reconnecting your wallet.',
            'transaction': 'Transaction failed. Please try again or check your wallet.',
            'api': 'Service temporarily unavailable. Please try again in a moment.',
            'gas': 'Unable to fetch gas prices. Using cached data.',
            'swap': 'Swap quote failed. Please try again with different parameters.',
            'nft': 'Unable to load NFT data. Please refresh the page.',
            'staking': 'Staking data unavailable. Please try again later.'
        };

        for (const [key, message] of Object.entries(messages)) {
            if (context.toLowerCase().includes(key)) {
                return message;
            }
        }

        return 'An unexpected error occurred. Please try again.';
    }

    showUserError(message, type = 'error') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--${type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info'});
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 350px;
            color: var(--text-primary);
            animation: slideIn 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                <div style="font-size: 1.25rem;">
                    ${type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}
                </div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 0.25rem;">
                        ${type === 'error' ? 'Error' : type === 'warning' ? 'Warning' : 'Info'}
                    </div>
                    <div style="font-size: 0.875rem; line-height: 1.4;">
                        ${message}
                    </div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: none;
                    border: none;
                    color: var(--text-secondary);
                    cursor: pointer;
                    font-size: 1.125rem;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                ">×</button>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, type === 'error' ? 8000 : 5000);
    }

    async retryOperation(operation, context = '', maxRetries = null) {
        const retries = maxRetries || this.retryConfig.maxRetries;
        let lastError;

        for (let attempt = 0; attempt <= retries; attempt++) {
            try {
                const result = await operation();
                
                if (attempt > 0) {
                    console.log(`[${context}] Operation succeeded on attempt ${attempt + 1}`);
                }
                
                return result;
            } catch (error) {
                lastError = error;
                
                if (attempt === retries) {
                    break;
                }

                const delay = Math.min(
                    this.retryConfig.baseDelay * Math.pow(2, attempt),
                    this.retryConfig.maxDelay
                );

                console.warn(`[${context}] Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error.message);
                await this.delay(delay);
            }
        }

        throw lastError;
    }

    startPerformanceTimer(label) {
        this.performanceMetrics.set(label, {
            startTime: performance.now(),
            endTime: null,
            duration: null
        });
    }

    endPerformanceTimer(label) {
        const metric = this.performanceMetrics.get(label);
        if (metric) {
            metric.endTime = performance.now();
            metric.duration = metric.endTime - metric.startTime;
            
            if (metric.duration > 5000) { 
                console.warn(`[Performance] Slow operation detected: ${label} took ${metric.duration.toFixed(2)}ms`);
            }
            
            return metric.duration;
        }
        return null;
    }

    validateEthereumAddress(address) {
        if (!address || typeof address !== 'string') {
            return false;
        }
        
        if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
            return false;
        }
        
        return true;
    }

    validateTokenAmount(amount, decimals = 18) {
        if (!amount || isNaN(amount) || amount <= 0) {
            return false;
        }
        
        const decimalPlaces = (amount.toString().split('.')[1] || '').length;
        return decimalPlaces <= decimals;
    }

    validateTransactionHash(hash) {
        if (!hash || typeof hash !== 'string') {
            return false;
        }
        
        return /^0x[a-fA-F0-9]{64}$/.test(hash);
    }

    formatNumber(num, decimals = 2) {
        if (num === null || num === undefined || isNaN(num)) {
            return '--';
        }
        
        if (num >= 1000000) {
            return (num / 1000000).toFixed(decimals) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(decimals) + 'K';
        } else {
            return num.toFixed(decimals);
        }
    }

    formatCurrency(amount, currency = 'USD', decimals = 2) {
        if (amount === null || amount === undefined || isNaN(amount)) {
            return '--';
        }
        
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(amount);
    }

    formatPercentage(value, decimals = 2) {
        if (value === null || value === undefined || isNaN(value)) {
            return '--';
        }
        
        const sign = value >= 0 ? '+' : '';
        return `${sign}${value.toFixed(decimals)}%`;
    }

    formatAddress(address, startChars = 6, endChars = 4) {
        if (!this.validateEthereumAddress(address)) {
            return 'Invalid Address';
        }
        
        return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
    }

    formatTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) return `${days}d ago`;
        if (hours > 0) return `${hours}h ago`;
        if (minutes > 0) return `${minutes}m ago`;
        return `${seconds}s ago`;
    }

    showLoadingState(elementId, message = 'Loading...') {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <div class="loading-state" style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 2rem;
                    color: var(--text-secondary);
                ">
                    <div class="spinner" style="
                        width: 20px;
                        height: 20px;
                        border: 2px solid var(--border-color);
                        border-top: 2px solid var(--text-accent);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    "></div>
                    <span>${message}</span>
                </div>
            `;
        }
    }

    hideLoadingState(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            const loadingState = element.querySelector('.loading-state');
            if (loadingState) {
                loadingState.remove();
            }
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    sendErrorToTracking(errorInfo) {
       
        console.log('Error logged:', errorInfo);
    }

    getErrorStats() {
        const stats = {
            total: this.errorLog.length,
            byContext: {},
            recent: this.errorLog.slice(-10)
        };

        this.errorLog.forEach(error => {
            stats.byContext[error.context] = (stats.byContext[error.context] || 0) + 1;
        });

        return stats;
    }
}

window.utils = new ProductionUtils();

const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductionUtils;
}

class FeesWTFApp {
    constructor() {
        this.currentView = 'dashboard';
        this.isWalletConnected = false;
        this.walletAddress = null;
        this.web3Provider = null;
        this.isConnecting = false;

        this.init();
    }

    async init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        try {
            this.initTheme();

            // Ensure DOM is ready before initializing navigation
            setTimeout(() => {
                this.initNavigation();
                this.initModals();

                // Initialize router after navigation is set up
                if (window.router) {
                    window.router.init();
                }
            }, 100);

            // Initialize wallet connection with delay for mobile
            setTimeout(() => {
                this.initWalletConnection();
            }, 200);

            this.startPeriodicUpdates();

        } catch (error) {
            this.showError('Failed to initialize application. Please refresh the page.');
        }
    }

    initTheme() {
        const themeToggle = document.getElementById('theme-toggle');
        const savedTheme = localStorage.getItem('fees-wtf-theme') || 'dark';
        
        document.body.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
        
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.body.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.body.setAttribute('data-theme', newTheme);
                localStorage.setItem('fees-wtf-theme', newTheme);
                this.updateThemeIcon(newTheme);
            });
        }
    }

    updateThemeIcon(theme) {
        const themeIcon = document.querySelector('.theme-icon');
        if (themeIcon) {
            themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    }

    initNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const route = link.getAttribute('data-route');
                if (route && window.router) {
                    window.router.navigate(route);
                }
            });
        });
    }

    initWalletConnection() {
        const connectBtn = document.getElementById('connect-wallet');
        const disconnectBtn = document.getElementById('disconnect-wallet');
        
        if (connectBtn) {
            connectBtn.addEventListener('click', () => {
                this.showConnectModal();
            });
        }
        
        if (disconnectBtn) {
            disconnectBtn.addEventListener('click', () => {
                this.disconnectWallet();
            });
        }
        
        this.checkExistingConnection();
    }

    initModals() {
        const connectModal = document.getElementById('connect-modal-overlay');
        const closeModal = document.getElementById('close-modal');
        
        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideConnectModal();
            });
        }
        
        if (connectModal) {
            connectModal.addEventListener('click', (e) => {
                if (e.target === connectModal) {
                    this.hideConnectModal();
                }
            });
        }
        
        const walletItems = document.querySelectorAll('.modal-item[data-wallet]');
        walletItems.forEach(item => {
            item.addEventListener('click', () => {
                const walletType = item.getAttribute('data-wallet');
                this.connectWallet(walletType);
            });
        });
    }

    showConnectModal() {
        const modal = document.getElementById('connect-modal-overlay');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    hideConnectModal() {
        const modal = document.getElementById('connect-modal-overlay');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    async connectWallet(walletType) {
        if (this.isConnecting) return;

        try {
            this.isConnecting = true;

            if (window.walletManager) {
                const result = await window.walletManager.connect(walletType);
                if (result.success) {
                    this.onWalletConnected(result.address, result.provider);
                    this.hideConnectModal();
                } else {
                    this.showError(result.error || 'Failed to connect wallet');
                }
            } else {
                this.showError('Wallet manager not initialized');
            }
        } catch (error) {
            this.showError('Failed to connect wallet. Please try again.');
        } finally {
            this.isConnecting = false;
        }
    }

    async checkExistingConnection() {
        if (!window.walletManager) return;

        // Add delay for mobile wallet detection
        await new Promise(resolve => setTimeout(resolve, 500));

        try {
            // First check for persisted connection
            const persistedWallet = localStorage.getItem('drip_wallet_connection');
            if (persistedWallet) {
                try {
                    const walletData = JSON.parse(persistedWallet);

                    // Try to reconnect to the persisted wallet (without requesting new connection)
                    if (walletData.type === 'metamask' && window.ethereum) {
                        const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                        if (accounts.length > 0) {
                            this.onWalletConnected(accounts[0], window.ethereum);
                            return;
                        }
                    } else if (walletData.type === 'phantom' && window.solana) {
                        try {
                            const response = await window.solana.connect({ onlyIfTrusted: true });
                            if (response.publicKey) {
                                this.onWalletConnected(response.publicKey.toString(), window.solana);
                                return;
                            }
                        } catch (e) {
                            // Silent fail for phantom trusted connection
                        }
                    }
                } catch (error) {
                    localStorage.removeItem('drip_wallet_connection');
                }
            }

            // Fallback to checking existing connections
            const connection = await window.walletManager.checkConnection();
            if (connection.isConnected) {
                this.onWalletConnected(connection.address, connection.provider);
            }
        } catch (error) {
            // Silent fail to prevent connection loops
        }
    }

    onWalletConnected(address, provider) {
        this.isWalletConnected = true;
        this.walletAddress = address;
        this.web3Provider = provider;
        
        this.updateWalletUI();

        // Update gas tracker wallet buttons
        if (window.gasTracker && window.gasTracker.updateWalletButtons) {
            window.gasTracker.updateWalletButtons();
        }
    }

    disconnectWallet() {
        if (window.walletManager) {
            window.walletManager.disconnect();
        }
        
        this.isWalletConnected = false;
        this.walletAddress = null;
        this.web3Provider = null;
        
        this.updateWalletUI();

        // Update gas tracker wallet buttons
        if (window.gasTracker && window.gasTracker.updateWalletButtons) {
            window.gasTracker.updateWalletButtons();
        }
    }

    updateWalletUI() {
        const connectBtn = document.getElementById('connect-wallet');
        const walletInfo = document.getElementById('wallet-info');
        const walletAddress = document.getElementById('wallet-address');
        const walletBalance = document.getElementById('wallet-balance');
        const disconnectBtn = document.getElementById('disconnect-wallet');

        if (this.isWalletConnected && this.walletAddress) {
            if (connectBtn) connectBtn.classList.add('hidden');
            if (walletInfo) walletInfo.classList.remove('hidden');

            // Show only the address
            if (walletAddress && window.utils) {
                walletAddress.textContent = window.utils.formatAddress(this.walletAddress);
            }

            // Hide balance and disconnect button
            if (walletBalance) walletBalance.style.display = 'none';
            if (disconnectBtn) disconnectBtn.style.display = 'none';
        } else {
            if (connectBtn) connectBtn.classList.remove('hidden');
            if (walletInfo) walletInfo.classList.add('hidden');

            // Reset visibility for when wallet disconnects
            if (walletBalance) walletBalance.style.display = '';
            if (disconnectBtn) disconnectBtn.style.display = '';
        }
    }

    async loadWalletData() {
        if (!this.isWalletConnected || !this.walletAddress) return;
        
        try {
            // Load ETH balance
            if (window.walletManager) {
                const balance = await window.walletManager.getBalance(this.walletAddress);
                const balanceElement = document.getElementById('wallet-balance');
                if (balanceElement && window.utils) {
                    balanceElement.textContent = `${window.utils.formatNumber(balance, 4)} ETH`;
                }
            }
        } catch (error) {
            // Silently handle wallet data errors
        }
    }

    startPeriodicUpdates() {
        // No periodic updates needed since we only show address
    }

    showError(message) {
        
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    showSuccess(message) {
        
        const notification = document.createElement('div');
        notification.className = 'success-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #44ff44;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}

window.feesWTFApp = new FeesWTFApp();

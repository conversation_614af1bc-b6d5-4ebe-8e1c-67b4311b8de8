class Router {
    constructor() {
        this.routes = {
            'home': 'dashboard-view',
            'dashboard': 'dashboard-view',
            'stake-drip': 'stake-drip-view',
            'stake-lp': 'stake-lp-view'
        };
        
        this.currentRoute = 'home';
        this.init();
    }

    init() {
        // Ensure DOM is ready before setting up routing
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupRouting();
            });
        } else {
            this.setupRouting();
        }
    }

    setupRouting() {
        window.addEventListener('hashchange', () => {
            this.handleRouteChange();
        });

        this.handleRouteChange();
    }

    handleRouteChange() {
        const hash = window.location.hash.slice(1); 
        let route = 'home';
        
        if (hash) {
            if (hash.startsWith('/')) {
                const pathParts = hash.slice(1).split('/');
                if (pathParts.length === 1) {
                    route = pathParts[0];
                } else if (pathParts.length === 2) {
                    route = `${pathParts[0]}-${pathParts[1]}`;
                }
            } else {
                route = hash;
            }
        }
        
        this.navigate(route, false);
    }

    navigate(route, updateHash = true) {
        if (!this.routes[route]) {
            console.warn(`Unknown route: ${route}, defaulting to home`);
            route = 'home';
        }
        
        if (updateHash) {
            const hashRoute = this.getHashFromRoute(route);
            window.location.hash = hashRoute;
        }
        
        const views = document.querySelectorAll('.view');
        views.forEach(view => {
            view.classList.remove('active');
        });
        
        const targetView = document.getElementById(this.routes[route]);
        if (targetView) {
            targetView.classList.add('active');
        }
        
        this.updateNavigation(route);
        
        this.currentRoute = route;
        
        this.initRouteSpecific(route);
        
        console.log(`📍 Navigated to: ${route}`);
    }

    getHashFromRoute(route) {
        const routeMap = {
            'home': '#/',
            'dashboard': '#/',
            'stake-drip': '#/stake/drip',
            'stake-lp': '#/stake/lp'
        };
        
        return routeMap[route] || '#/';
    }

    updateNavigation(route) {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-route="${route}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        } else {
            const homeLink = document.querySelector('[data-route="home"]');
            if (homeLink) {
                homeLink.classList.add('active');
            }
        }
    }

    initRouteSpecific(route) {
        switch (route) {
            case 'dashboard':
            case 'home':
                this.initDashboard();
                break;
            case 'stake-drip':
                this.initStakeDRIP();
                break;
            case 'stake-lp':
                this.initStakeLP();
                break;
        }
    }

    initDashboard() {
        if (window.gasTracker && !window.gasTracker.chartInitialized) {
            window.gasTracker.initChart();
        }
        
        if (window.gasTracker) {
            window.gasTracker.updateGasPrices();
        }
    }

    initStakeDRIP() {
        if (window.stakingManager) {
            window.stakingManager.initDripMeStaking();
        }

        if (window.feesWTFApp && window.feesWTFApp.isWalletConnected) {
            this.loadStakingData('dripme');
        }
    }

    initStakeLP() {
        if (window.stakingManager) {
            window.stakingManager.initLPStaking();
        }
        
        if (window.feesWTFApp && window.feesWTFApp.isWalletConnected) {
            this.loadStakingData('lp');
        }
    }

    initSwap() {
        if (window.swapManager) {
            window.swapManager.init();
        }
        
        if (window.feesWTFApp && window.feesWTFApp.isWalletConnected) {
            this.loadSwapData();
        }
    }

    initNFT() {
        if (window.nftManager) {
            window.nftManager.init();
        }
        
        this.loadNFTData();
    }

    async loadStakingData(type) {
        try {
            if (window.stakingManager) {
                await window.stakingManager.loadUserData(type);
            }
        } catch (error) {
            console.error(`Error loading ${type} staking data:`, error);
        }
    }

    async loadSwapData() {
        try {
            if (window.swapManager) {
                await window.swapManager.loadTokenBalances();
            }
        } catch (error) {
            console.error('Error loading swap data:', error);
        }
    }

    async loadNFTData() {
        try {
            if (window.nftManager) {
                await window.nftManager.loadCollectionData();
            }
        } catch (error) {
            console.error('Error loading NFT data:', error);
        }
    }

    getCurrentRoute() {
        return this.currentRoute;
    }

    isOnRoute(route) {
        return this.currentRoute === route;
    }
}

window.router = new Router();

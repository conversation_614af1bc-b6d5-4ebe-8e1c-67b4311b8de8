{"name": "fees-wtf", "version": "1.0.0", "description": "Ethereum Gas Tracker & DeFi Tools - A comprehensive web application for tracking gas prices and interacting with DeFi protocols", "main": "index.html", "scripts": {"start": "npx serve . -p 8000", "dev": "npx serve . -p 8000 --cors", "build": "echo 'No build step required for this vanilla JS application'", "serve": "python3 start-server.py || python start-server.py"}, "keywords": ["ethereum", "gas-tracker", "defi", "web3", "staking", "swap", "nft", "dapp"], "author": "DripMe Team", "license": "MIT", "devDependencies": {"serve": "^14.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/fees-wtf.git"}, "bugs": {"url": "https://github.com/your-username/fees-wtf/issues"}, "homepage": "https://DripMe", "browserslist": ["> 1%", "last 2 versions", "not dead"]}
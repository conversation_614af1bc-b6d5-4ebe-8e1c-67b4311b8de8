class StakingManager {
    constructor() {
        this.dripmeStakingData = {
            userStaked: 0,
            pendingRewards: 0,
            totalStaked: 0,
            apy: 0
        };
        
        this.lpStakingData = {
            userLPBalance: 0,
            userStaked: 0,
            pendingRewards: 0,
            apy: 0
        };
        
        this.init();
    }

    init() {

        this.initDripMeStaking();
        this.initLPStaking();

    }

    initDripMeStaking() {
        const stakeBtn = document.getElementById('stake-wtf-btn');
        const unstakeBtn = document.getElementById('unstake-wtf-btn');
        const claimBtn = document.getElementById('claim-wtf-btn');
        const maxBtn = document.getElementById('max-stake-wtf');
        const amountInput = document.getElementById('stake-amount-wtf');

        if (stakeBtn) {
            stakeBtn.addEventListener('click', () => this.stakeDripMe());
        }

        if (unstakeBtn) {
            unstakeBtn.addEventListener('click', () => this.unstakeDripMe());
        }

        if (claimBtn) {
            claimBtn.addEventListener('click', () => this.claimDripMeRewards());
        }

        if (maxBtn && amountInput) {
            maxBtn.addEventListener('click', () => {
                amountInput.value = this.getMaxDripMeBalance();
            });
        }

        this.loadDripMeStakingData();
    }

    initLPStaking() {
        const stakeBtn = document.getElementById('stake-lp-btn');
        const unstakeBtn = document.getElementById('unstake-lp-btn');
        const claimBtn = document.getElementById('claim-lp-btn');
        const maxBtn = document.getElementById('max-stake-lp');
        const amountInput = document.getElementById('stake-amount-lp');

        if (stakeBtn) {
            stakeBtn.addEventListener('click', () => this.stakeLP());
        }

        if (unstakeBtn) {
            unstakeBtn.addEventListener('click', () => this.unstakeLP());
        }

        if (claimBtn) {
            claimBtn.addEventListener('click', () => this.claimLPRewards());
        }

        if (maxBtn && amountInput) {
            maxBtn.addEventListener('click', () => {
                amountInput.value = this.getMaxLPBalance();
            });
        }

        this.loadLPStakingData();
    }

    async loadDripMeStakingData() {
        try {


            this.dripmeStakingData = {
                userStaked: 0,
                pendingRewards: 0,
                totalStaked: 1000000,
                apy: 25.5
            };

            this.updateDripMeStakingUI();

        } catch (error) {
            console.error('Error loading DripMe staking data:', error);
        }
    }

    async loadLPStakingData() {
        try {
           
            
            this.lpStakingData = {
                userLPBalance: 0,
                userStaked: 0,
                pendingRewards: 0,
                apy: 45.2 
            };

            this.updateLPStakingUI();
            
        } catch (error) {
            console.error('Error loading LP staking data:', error);
        }
    }

    updateDripMeStakingUI() {
        const userStaked = document.getElementById('user-staked-drip');
        const pendingRewards = document.getElementById('pending-rewards-drip');
        const totalStaked = document.getElementById('total-staked-drip');
        const apy = document.getElementById('dripme-apy');

        if (userStaked && window.utils) {
            userStaked.textContent = `${window.utils.formatNumber(this.dripmeStakingData.userStaked)} $DRIP`;
        }

        if (pendingRewards && window.utils) {
            pendingRewards.textContent = `${window.utils.formatNumber(this.dripmeStakingData.pendingRewards)} $DRIP`;
        }

        if (totalStaked && window.utils) {
            totalStaked.textContent = `${window.utils.formatNumber(this.dripmeStakingData.totalStaked)} $DRIP`;
        }

        if (apy) {
            apy.textContent = this.dripmeStakingData.apy.toFixed(1);
        }
    }

    updateLPStakingUI() {
        const userLPBalance = document.getElementById('user-lp-balance');
        const userStaked = document.getElementById('user-staked-lp');
        const pendingRewards = document.getElementById('pending-rewards-lp');
        const apy = document.getElementById('lp-apy');

        if (userLPBalance && window.utils) {
            userLPBalance.textContent = `${window.utils.formatNumber(this.lpStakingData.userLPBalance)} LP`;
        }

        if (userStaked && window.utils) {
            userStaked.textContent = `${window.utils.formatNumber(this.lpStakingData.userStaked)} LP`;
        }

        if (pendingRewards && window.utils) {
            pendingRewards.textContent = `${window.utils.formatNumber(this.lpStakingData.pendingRewards)} $DRIP`;
        }

        if (apy) {
            apy.textContent = this.lpStakingData.apy.toFixed(1);
        }
    }

    async stakeDripMe() {
        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            window.feesWTFApp.showError('Please connect your wallet first');
            return;
        }

        const amountInput = document.getElementById('stake-amount-wtf');
        const amount = parseFloat(amountInput.value);

        if (!amount || amount <= 0) {
            window.feesWTFApp.showError('Please enter a valid amount');
            return;
        }

        try {


            await this.simulateTransaction();

            window.feesWTFApp.showSuccess(`Successfully staked ${amount} $DRIP tokens!`);

            this.dripmeStakingData.userStaked += amount;
            this.updateDripMeStakingUI();

            amountInput.value = '';

        } catch (error) {
            console.error('Error staking DripMe:', error);
            window.feesWTFApp.showError('Failed to stake $DRIP tokens');
        }
    }

    async unstakeDripMe() {
        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            window.feesWTFApp.showError('Please connect your wallet first');
            return;
        }

        const amountInput = document.getElementById('stake-amount-wtf');
        const amount = parseFloat(amountInput.value);

        if (!amount || amount <= 0) {
            window.feesWTFApp.showError('Please enter a valid amount');
            return;
        }

        if (amount > this.dripmeStakingData.userStaked) {
            window.feesWTFApp.showError('Insufficient staked balance');
            return;
        }

        try {

            await this.simulateTransaction();

            window.feesWTFApp.showSuccess(`Successfully unstaked ${amount} $DRIP tokens!`);

            this.dripmeStakingData.userStaked -= amount;
            this.updateDripMeStakingUI();

            amountInput.value = '';

        } catch (error) {
            console.error('Error unstaking DripMe:', error);
            window.feesWTFApp.showError('Failed to unstake $DRIP tokens');
        }
    }

    async claimDripMeRewards() {
        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            window.feesWTFApp.showError('Please connect your wallet first');
            return;
        }

        if (this.dripmeStakingData.pendingRewards <= 0) {
            window.feesWTFApp.showError('No rewards to claim');
            return;
        }

        try {

            await this.simulateTransaction();

            const rewards = this.dripmeStakingData.pendingRewards;
            window.feesWTFApp.showSuccess(`Successfully claimed ${rewards.toFixed(4)} $DRIP rewards!`);

            this.dripmeStakingData.pendingRewards = 0;
            this.updateDripMeStakingUI();

        } catch (error) {
            console.error('Error claiming DripMe rewards:', error);
            window.feesWTFApp.showError('Failed to claim rewards');
        }
    }

    async stakeLP() {
        window.feesWTFApp.showError('LP staking functionality coming soon');
    }

    async unstakeLP() {
        window.feesWTFApp.showError('LP unstaking functionality coming soon');
    }

    async claimLPRewards() {
        window.feesWTFApp.showError('LP rewards claiming functionality coming soon');
    }

    getMaxDripMeBalance() {
        return 1000;
    }

    getMaxLPBalance() {
        return 100; 
    }

    async simulateTransaction() {
        return new Promise(resolve => setTimeout(resolve, 2000));
    }

    async loadUserData(type) {
        if (type === 'dripme') {
            await this.loadDripMeStakingData();
        } else if (type === 'lp') {
            await this.loadLPStakingData();
        }
    }
}

window.stakingManager = new StakingManager();

class GasTracker {
    constructor() {
        this.gasData = {
            fast: 0,
            standard: 0,
            safe: 0
        };

        this.ethPrice = 0;
        this.ethChange = 0;
        this.chart = null;
        this.chartInitialized = false;
        this.gasHistory = [];

        this.userGasData = {
            totalGasSpent: 0,
            totalTransactions: 0,
            totalGasUsed: 0,
            failedTransactions: 0,
            avgGasPrice: 0,
            address: ''
        };

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        console.log('⛽ Initializing Gas Tracker...');

        this.showFallbackGasData();

        this.initChart();
        this.initUserGasTracking();

        this.loadExampleGasData();

        this.updateGasPrices();
        this.updateETHPrice();

        setInterval(() => {
            this.updateGasPrices();
        }, 30000);

        setInterval(() => {
            this.updateETHPrice();
        }, 60000);

        // Hook into wallet connection events
        this.setupWalletHooks();
    }

    initUserGasTracking() {
        console.log('🔧 Initializing user gas tracking...');

        const heroTrackBtn = document.getElementById('hero-track-gas');
        const heroConnectBtn = document.getElementById('hero-connect-wallet');
        const analyticsConnectBtn = document.getElementById('connect-wallet-analytics');

        console.log('🔍 Hero track button found:', !!heroTrackBtn);
        console.log('🔍 Hero connect button found:', !!heroConnectBtn);
        console.log('🔍 Analytics connect button found:', !!analyticsConnectBtn);

        // Hero section buttons
        if (heroTrackBtn) {
            heroTrackBtn.addEventListener('click', () => {
                // Scroll to analytics section
                const analyticsSection = document.querySelector('.analytics-section');
                if (analyticsSection) {
                    analyticsSection.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }

        if (heroConnectBtn) {
            heroConnectBtn.addEventListener('click', () => {
                // Trigger wallet connection
                const connectBtn = document.getElementById('connect-wallet');
                if (connectBtn) {
                    connectBtn.click();
                }
            });
        }

        if (analyticsConnectBtn) {
            analyticsConnectBtn.addEventListener('click', () => {
                // Trigger wallet connection
                const connectBtn = document.getElementById('connect-wallet');
                if (connectBtn) {
                    connectBtn.click();
                }
            });
        }

        console.log('✅ User gas tracking event listeners added');
    }

    setupWalletHooks() {
        // Hook into the main app's wallet connection
        const originalOnWalletConnected = window.feesWTFApp && window.feesWTFApp.onWalletConnected?.bind(window.feesWTFApp);
        if (window.feesWTFApp) {
            window.feesWTFApp.onWalletConnected = (address, provider) => {
                if (originalOnWalletConnected) {
                    originalOnWalletConnected(address, provider);
                }
                // Update gas tracking for connected wallet
                this.onWalletConnected(address);
            };
        }

        // If already connected, update immediately
        if (window.feesWTFApp && window.feesWTFApp.walletAddress) {
            this.onWalletConnected(window.feesWTFApp.walletAddress);
        }
    }

    async onWalletConnected(address) {
        console.log('🔗 Wallet connected to gas tracker:', address);

        // Update the tracked address display immediately
        const trackedAddress = document.getElementById('tracked-address');
        if (trackedAddress) {
            trackedAddress.textContent = this.formatAddress(address);
            trackedAddress.classList.add('connected');

            const statItem = trackedAddress.closest('.stat-item');
            if (statItem) {
                statItem.classList.add('connected');
            }

            const labelElement = trackedAddress.parentElement.querySelector('.stat-label');
            const secondaryElement = trackedAddress.parentElement.querySelector('.stat-secondary');
            if (labelElement) labelElement.textContent = 'Connected Address';
            if (secondaryElement) secondaryElement.textContent = 'Live data';
        }

        // Fetch and update gas data for the connected address
        await this.trackUserGas(address);
    }

    isValidEthereumAddress(address) {
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }

    async updateGasPrices() {
        console.log('🔄 Updating gas prices...');
        try {
            const gasData = await this.fetchGasPrices();

            if (gasData) {
                console.log('✅ Gas data received:', gasData);
                this.gasData = gasData;
                this.updateGasUI();
                this.updateGasHistory();
            } else {
                console.warn('⚠️ No gas data received, using fallback');
                this.showFallbackGasData();
            }
        } catch (error) {
            console.error('❌ Error updating gas prices:', error);
            this.showFallbackGasData();
        }
    }

    async fetchGasPrices() {
        console.log('🔍 Fetching gas prices from APIs...');

        try {
            console.log('📡 Trying Etherscan API...');
            const response = await fetch('https://api.etherscan.io/api?module=gastracker&action=gasoracle&apikey=R5DETVFH5FCEIIW44RFJM3EAFB86RNZTZN');
            const data = await response.json();

            console.log('📊 Etherscan response:', data);

            if (data.status === '1' && data.result) {
                const safeGas = parseInt(data.result.SafeGasPrice);
                const gasData = {
                    fast: Math.round(safeGas * 2), // Dynamic: 2x safe gas
                    standard: parseInt(data.result.StandardGasPrice),
                    safe: safeGas
                };
                console.log('✅ Etherscan gas data:', gasData);
                return gasData;
            }
        } catch (error) {
            console.warn('⚠️ Etherscan API failed:', error);
        }

        try {
            console.log('📡 Trying ETH Gas Station API...');
            const response = await fetch('https://ethgasstation.info/api/ethgasAPI.json');
            const data = await response.json();

            console.log('📊 ETH Gas Station response:', data);

            const gasData = {
                fast: Math.round(data.fast / 10),
                standard: Math.round(data.average / 10),
                safe: Math.round(data.safeLow / 10)
            };
            console.log('✅ ETH Gas Station gas data:', gasData);
            return gasData;
        } catch (error) {
            console.warn('⚠️ ETH Gas Station API failed:', error);
        }

        console.error('❌ All gas price APIs failed');
        return null;
    }

    showFallbackGasData() {
        console.log('📊 Using fallback gas data');
        this.gasData = {
            fast: 35,
            standard: 25,
            safe: 18
        };
        this.ethPrice = 3794.94;
        this.ethChange = 0.82;
        this.updateGasUI();
        this.updateETHPriceUI();
    }

    updateGasUI() {
        console.log('🔄 Updating gas UI with data:', this.gasData);

        const currentGas = document.getElementById('current-gas');
        if (currentGas) {
            currentGas.textContent = this.gasData.fast;
        }

        this.updateGasCard('fast-gas', this.gasData.fast, '~15 seconds');
        this.updateGasCard('standard-gas', this.gasData.standard, '~3 minutes');
        this.updateGasCard('safe-gas', this.gasData.safe, '~5 minutes');

        this.updateHeroPreview();
    }

    updateHeroPreview() {
        const heroSafeGas = document.getElementById('hero-safe-gas');
        const heroStandardGas = document.getElementById('hero-standard-gas');
        const heroFastGas = document.getElementById('hero-fast-gas');
        const heroEthPrice = document.getElementById('hero-eth-price');

        if (heroSafeGas) heroSafeGas.textContent = `${this.gasData.safe} gwei`;
        if (heroStandardGas) heroStandardGas.textContent = `${this.gasData.standard} gwei`;
        if (heroFastGas) heroFastGas.textContent = `${this.gasData.fast} gwei`;
        if (heroEthPrice && this.ethPrice > 0) {
            heroEthPrice.textContent = `$${this.ethPrice.toLocaleString()}`;
        }
    }

    updateGasCard(cardId, gasPrice, time) {
        const card = document.getElementById(cardId);
        if (!card) {
            console.warn(`Gas card not found: ${cardId}`);
            return;
        }

        const priceElement = card.querySelector('.gas-price');
        const breakdownElement = card.querySelector('.gas-breakdown');
        const costElement = card.querySelector('.gas-cost');

        if (priceElement) {
            priceElement.textContent = `${gasPrice} gwei`;
        }

        if (breakdownElement) {
            const baseFee = Math.round(gasPrice * 0.7);
            const priorityFee = gasPrice - baseFee;
            breakdownElement.textContent = `Base: ${baseFee} | Priority: ${priorityFee}`;
        }

        if (costElement && this.ethPrice > 0) {
            const cost = this.estimateTransactionCost(21000, gasPrice);
            costElement.textContent = `$${cost.usd.toFixed(2)} | ${time}`;
        } else if (costElement) {
            costElement.textContent = `$-- | ${time}`;
        }
    }

    async updateETHPrice() {
        try {
            const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd&include_24hr_change=true');
            const data = await response.json();
            
            if (data.ethereum) {
                this.ethPrice = data.ethereum.usd;
                this.ethChange = data.ethereum.usd_24h_change || 0;
                this.updateETHPriceUI();
            }
        } catch (error) {
            console.error('Error updating ETH price:', error);
            this.ethPrice = 2000; 
            this.ethChange = 0;
            this.updateETHPriceUI();
        }
    }

    updateETHPriceUI() {
        const ethPrice = document.getElementById('eth-price');
        const ethChange = document.getElementById('eth-change');

        if (ethPrice && window.utils) {
            ethPrice.textContent = window.utils.formatCurrency(this.ethPrice);
        } else if (ethPrice) {
            ethPrice.textContent = `$${this.ethPrice.toLocaleString()}`;
        }

        if (ethChange && window.utils) {
            const changeText = window.utils.formatPercentage(this.ethChange);
            ethChange.textContent = changeText;

            ethChange.className = 'stat-change';
            if (this.ethChange > 0) {
                ethChange.classList.add('positive');
            } else if (this.ethChange < 0) {
                ethChange.classList.add('negative');
            }
        } else if (ethChange) {
            ethChange.textContent = `${this.ethChange > 0 ? '+' : ''}${this.ethChange.toFixed(2)}%`;
        }

        this.updateHeroPreview();
    }

    initChart() {
        if (this.chartInitialized) return;
        
        const chartCanvas = document.getElementById('gas-chart');
        if (!chartCanvas || !window.Chart) {
            console.warn('Chart.js not loaded or canvas not found');
            return;
        }

        const ctx = chartCanvas.getContext('2d');
        
        const labels = [];
        const fastData = [];
        const standardData = [];
        const safeData = [];
        
        for (let i = 23; i >= 0; i--) {
            const time = new Date();
            time.setHours(time.getHours() - i);
            labels.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));
            
            fastData.push(Math.floor(Math.random() * 20) + 20);
            standardData.push(Math.floor(Math.random() * 15) + 15);
            safeData.push(Math.floor(Math.random() * 10) + 10);
        }

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Fast',
                        data: fastData,
                        borderColor: '#ff6b6b',
                        backgroundColor: 'rgba(255, 107, 107, 0.1)',
                        tension: 0.4,
                        fill: false
                    },
                    {
                        label: 'Standard',
                        data: standardData,
                        borderColor: '#4ecdc4',
                        backgroundColor: 'rgba(78, 205, 196, 0.1)',
                        tension: 0.4,
                        fill: false
                    },
                    {
                        label: 'Safe',
                        data: safeData,
                        borderColor: '#45b7d1',
                        backgroundColor: 'rgba(69, 183, 209, 0.1)',
                        tension: 0.4,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: 'var(--text-color)',
                            usePointStyle: true
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'var(--border-color)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)'
                        }
                    },
                    y: {
                        grid: {
                            color: 'var(--border-color)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)',
                            callback: function(value) {
                                return value + ' gwei';
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        this.chartInitialized = true;
        console.log('📊 Gas price chart initialized');
    }

    updateGasHistory() {
        if (!this.chart) return;

        const now = new Date();
        const timeLabel = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

        this.chart.data.labels.push(timeLabel);
        this.chart.data.datasets[0].data.push(this.gasData.fast);
        this.chart.data.datasets[1].data.push(this.gasData.standard);
        this.chart.data.datasets[2].data.push(this.gasData.safe);

        if (this.chart.data.labels.length > 24) {
            this.chart.data.labels.shift();
            this.chart.data.datasets.forEach(dataset => {
                dataset.data.shift();
            });
        }

        this.chart.update('none'); 
    }

    getCurrentGasPrices() {
        return { ...this.gasData };
    }

    estimateTransactionCost(gasLimit = 21000, gasPrice = null) {
        const price = gasPrice || this.gasData.standard;
        const costInWei = gasLimit * price * 1e9;
        const costInETH = costInWei / 1e18;
        const costInUSD = costInETH * this.ethPrice;

        return {
            eth: costInETH,
            usd: costInUSD,
            gwei: price
        };
    }

    async trackUserGas(address) {
        this.showLoading();
        this.hideError();

        // Show loading state in the address display
        const trackedAddress = document.getElementById('tracked-address');
        if (trackedAddress) {
            const secondaryElement = trackedAddress.parentElement.querySelector('.stat-secondary');
            if (secondaryElement) {
                secondaryElement.textContent = 'Fetching data...';
            }
        }

        try {
            const transactions = await this.fetchUserTransactions(address);
            if (transactions && transactions.length > 0) {
                this.calculateGasStats(transactions, address);
                this.updateUserGasUI();
                this.showGasStats();
            } else {
                this.showError('No transactions found for this address');
            }
        } catch (error) {
            console.error('Error tracking user gas:', error);
            this.showError('Failed to fetch transaction data. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    async fetchUserTransactions(address) {
        const apiKey = 'T9RV3FGW573WX9YX45F1Z89MEMEUNQXUC7';
        const url = `https://api.etherscan.io/api?module=account&action=txlist&address=${address}&startblock=0&endblock=********&sort=asc&apikey=${apiKey}`;

        try {
            const response = await fetch(url);
            const data = await response.json();

            if (data.status === '1' && data.result) {
                return data.result;
            } else {
                throw new Error(data.message || 'Failed to fetch transactions');
            }
        } catch (error) {
            console.error('Etherscan API error:', error);
            throw error;
        }
    }

    calculateGasStats(transactions, address) {
        let totalGasSpent = 0;
        let totalGasUsed = 0;
        let totalGasPrice = 0;
        let failedCount = 0;

        transactions.forEach(tx => {
            const gasUsed = parseInt(tx.gasUsed);
            const gasPrice = parseInt(tx.gasPrice);
            const isError = tx.isError === '1';

            if (isError) {
                failedCount++;
            }

            const gasCostWei = gasUsed * gasPrice;
            const gasCostETH = gasCostWei / 1e18;

            totalGasSpent += gasCostETH;
            totalGasUsed += gasUsed;
            totalGasPrice += gasPrice;
        });

        this.userGasData = {
            totalGasSpent: totalGasSpent,
            totalTransactions: transactions.length,
            totalGasUsed: totalGasUsed,
            failedTransactions: failedCount,
            avgGasPrice: totalGasPrice / transactions.length / 1e9, 
            address: address
        };
    }

    loadExampleGasData() {
        console.log('📊 Loading example gas data...');

        this.userGasData = {
            totalGasSpent: 761.625,
            totalTransactions: 3,
            totalGasUsed: 63000,
            failedTransactions: 0,
            avgGasPrice: 12.0893,
            address: '0xF0f35…07708'
        };

        this.updateUserGasUI();
        this.showGasStats();

        console.log('✅ Example gas data loaded');
    }

    updateUserGasUI() {
        const totalGasEth = document.getElementById('total-gas-eth');
        const totalGasUsd = document.getElementById('total-gas-usd');
        const totalTransactions = document.getElementById('total-transactions');
        const avgGasPrice = document.getElementById('avg-gas-price');
        const totalGasUsed = document.getElementById('total-gas-used');
        const failedTransactions = document.getElementById('failed-transactions');
        const trackedAddress = document.getElementById('tracked-address');

        if (totalGasEth) {
            totalGasEth.textContent = `Ξ${this.userGasData.totalGasSpent.toFixed(3)}`;
        }

        if (totalGasUsd && this.ethPrice > 0) {
            const usdValue = this.userGasData.totalGasSpent * this.ethPrice;
            totalGasUsd.textContent = `$${usdValue.toFixed(2)}`;
        } else if (totalGasUsd) {
            totalGasUsd.textContent = '$-';
        }

        if (totalTransactions) {
            totalTransactions.textContent = this.userGasData.totalTransactions.toLocaleString();
        }

        if (avgGasPrice) {
            avgGasPrice.textContent = `Avg: ${this.userGasData.avgGasPrice.toFixed(4)}M Gwei`;
        }

        if (totalGasUsed) {
            totalGasUsed.textContent = this.userGasData.totalGasUsed.toLocaleString();
        }

        if (failedTransactions) {
            failedTransactions.textContent = `${this.userGasData.failedTransactions} failed`;
        }

        if (trackedAddress) {
            if (this.userGasData.address === '0xF0f35…07708') {
                trackedAddress.textContent = 'No account connected';
                trackedAddress.classList.remove('connected');

                const statItem = trackedAddress.closest('.stat-item');
                if (statItem) {
                    statItem.classList.remove('connected');
                }

                const labelText = trackedAddress.parentElement.querySelector('.stat-label');
                const secondaryText = trackedAddress.parentElement.querySelector('.stat-secondary');
                if (labelText) labelText.textContent = 'Example Address';
                if (secondaryText) {
                    secondaryText.textContent = `using ${this.userGasData.address} as an example`;
                }
            } else {
                trackedAddress.textContent = this.formatAddress(this.userGasData.address);
                trackedAddress.classList.add('connected');

                const statItem = trackedAddress.closest('.stat-item');
                if (statItem) {
                    statItem.classList.add('connected');
                }

                const labelText = trackedAddress.parentElement.querySelector('.stat-label');
                const secondaryText = trackedAddress.parentElement.querySelector('.stat-secondary');
                if (labelText) labelText.textContent = 'Connected Address';
                if (secondaryText) {
                    secondaryText.textContent = 'Live data';
                }
            }
        }
    }

    formatAddress(address) {
        if (address.length <= 10) return address;
        return `${address.slice(0, 6)}…${address.slice(-6)}`;
    }

    showGasStats() {
        console.log('👁️ Showing gas stats...');
        const gasStats = document.getElementById('gas-stats');
        console.log('🔍 Gas stats element found:', !!gasStats);
        if (gasStats) {
            gasStats.classList.remove('hidden');
            console.log('✅ Gas stats shown');
        } else {
            console.warn('⚠️ Gas stats element not found');
        }
    }

    hideGasStats() {
        const gasStats = document.getElementById('gas-stats');
        if (gasStats) {
            gasStats.classList.add('hidden');
        }
    }

    showLoading() {
        const loading = document.getElementById('loading-gas');
        if (loading) {
            loading.classList.remove('hidden');
        }
    }

    hideLoading() {
        const loading = document.getElementById('loading-gas');
        if (loading) {
            loading.classList.add('hidden');
        }
    }

    showError(message) {
        const error = document.getElementById('gas-error');
        if (error) {
            const errorText = error.querySelector('span:last-child');
            if (errorText) {
                errorText.textContent = message;
            }
            error.classList.remove('hidden');
        }
    }

    hideError() {
        const error = document.getElementById('gas-error');
        if (error) {
            error.classList.add('hidden');
        }
    }
}

window.gasTracker = new GasTracker();

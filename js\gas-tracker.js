class GasTracker {
    constructor() {
        this.gasData = {
            fast: 0,
            standard: 0,
            safe: 0
        };

        this.ethPrice = 0;
        this.ethChange = 0;
        this.chart = null;
        this.chartInitialized = false;
        this.gasHistory = [];

        // Rate limiting for API calls
        this.lastApiCall = 0;
        this.minApiInterval = 2000; // 2 seconds between calls

        this.userGasData = {
            totalGasSpent: 0,
            totalTransactions: 0,
            totalGasUsed: 0,
            failedTransactions: 0,
            avgGasPrice: 0,
            address: ''
        };

        // Load saved gas data from localStorage
        this.loadSavedGasData();

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        this.initChart();
        this.initUserGasTracking();

        // Show loading state initially
        this.showGasLoading();
        this.showETHLoading();

        // Load cached values from localStorage unless this is a hard reload
        try {
            const navEntry = performance.getEntriesByType('navigation')[0];
            const isReload = navEntry && navEntry.type === 'reload';
            if (!isReload) {
                const cachedGas = this.loadGasPricesFromStorage();
                if (cachedGas) {
                    this.gasData = cachedGas;
                    this.updateGasUI();
                }
                const cachedEth = this.loadETHPriceFromStorage();
                if (cachedEth) {
                    this.ethPrice = cachedEth.usd;
                    this.ethChange = cachedEth.change || 0;
                    this.updateETHPriceUI();
                }
            }
        } catch (e) {
            // Silently handle localStorage errors
        }

        this.updateGasPrices();
        this.updateETHPrice();

        setInterval(() => {
            this.updateGasPrices();
        }, 30000);

        setInterval(() => {
            this.updateETHPrice();
        }, 60000);

        // Hook into wallet connection events
        this.setupWalletHooks();
    }

    initUserGasTracking() {
        const heroTrackBtn = document.getElementById('hero-track-gas');
        const heroConnectBtn = document.getElementById('hero-connect-wallet');
        const analyticsConnectBtn = document.getElementById('connect-wallet-analytics');

        // Hero section buttons
        if (heroTrackBtn) {
            heroTrackBtn.addEventListener('click', () => {
                // Scroll to analytics section
                const analyticsSection = document.querySelector('.analytics-section');
                if (analyticsSection) {
                    analyticsSection.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }

        if (heroConnectBtn) {
            heroConnectBtn.addEventListener('click', () => {
                if (window.feesWTFApp && window.feesWTFApp.isWalletConnected) {
                    // Already connected, show disconnect option
                    window.feesWTFApp?.disconnectWallet();
                } else {
                    // Trigger wallet connection
                    const connectBtn = document.getElementById('connect-wallet');
                    if (connectBtn) {
                        connectBtn.click();
                    }
                }
            });
        }

        if (analyticsConnectBtn) {
            analyticsConnectBtn.addEventListener('click', () => {
                if (window.feesWTFApp && window.feesWTFApp.isWalletConnected) {
                    // Already connected, start tracking
                    this.trackUserGas(window.feesWTFApp.walletAddress);
                } else {
                    // Trigger wallet connection
                    const connectBtn = document.getElementById('connect-wallet');
                    if (connectBtn) {
                        connectBtn.click();
                    }
                }
            });
        }

        // Update button states initially and on wallet changes
        this.updateWalletButtons();
    }

    updateWalletButtons() {
        const heroConnectBtn = document.getElementById('hero-connect-wallet');
        const analyticsConnectBtn = document.getElementById('connect-wallet-analytics');

        if (window.feesWTFApp && window.feesWTFApp.isWalletConnected) {
            // Update hero connect button
            if (heroConnectBtn) {
                const buttonIcon = heroConnectBtn.querySelector('.button-icon');
                const buttonText = heroConnectBtn.childNodes[heroConnectBtn.childNodes.length - 1];

                if (buttonIcon) buttonIcon.textContent = '🔗';
                if (buttonText && buttonText.nodeType === Node.TEXT_NODE) {
                    buttonText.textContent = ` ${window.utils?.formatAddress(window.feesWTFApp.walletAddress) || 'Connected'}`;
                } else {
                    heroConnectBtn.innerHTML = '<span class="button-icon">🔗</span> ' +
                        (window.utils?.formatAddress(window.feesWTFApp.walletAddress) || 'Connected');
                }
            }

            // Update analytics connect button
            if (analyticsConnectBtn) {
                const buttonIcon = analyticsConnectBtn.querySelector('.button-icon');
                const buttonText = analyticsConnectBtn.childNodes[analyticsConnectBtn.childNodes.length - 1];

                if (buttonIcon) buttonIcon.textContent = '📊';
                if (buttonText && buttonText.nodeType === Node.TEXT_NODE) {
                    buttonText.textContent = ` Track Gas for ${window.utils?.formatAddress(window.feesWTFApp.walletAddress) || 'Connected Wallet'}`;
                } else {
                    analyticsConnectBtn.innerHTML = '<span class="button-icon">📊</span> Track Gas for ' +
                        (window.utils?.formatAddress(window.feesWTFApp.walletAddress) || 'Connected Wallet');
                }
            }
        } else {
            // Reset to original state
            if (heroConnectBtn) {
                heroConnectBtn.innerHTML = '<span class="button-icon">👛</span> Connect Wallet';
            }

            if (analyticsConnectBtn) {
                analyticsConnectBtn.innerHTML = '<span class="button-icon">👛</span> Connect Wallet to Track Gas';
            }
        }
    }

    setupWalletHooks() {
        // Hook into the main app's wallet connection
        const originalOnWalletConnected = window.feesWTFApp && window.feesWTFApp.onWalletConnected?.bind(window.feesWTFApp);
        if (window.feesWTFApp) {
            window.feesWTFApp.onWalletConnected = (address, provider) => {
                if (originalOnWalletConnected) {
                    originalOnWalletConnected(address, provider);
                }
                // Update gas tracking for connected wallet
                this.onWalletConnected(address);
            };
        }

        // If already connected, update immediately
        if (window.feesWTFApp && window.feesWTFApp.walletAddress) {
            this.onWalletConnected(window.feesWTFApp.walletAddress);
        }
    }

    async onWalletConnected(address) {

        // Update the tracked address display immediately
        const trackedAddress = document.getElementById('tracked-address');
        if (trackedAddress) {
            trackedAddress.textContent = this.formatAddress(address);
            trackedAddress.classList.add('connected');

            const statItem = trackedAddress.closest('.stat-item');
            if (statItem) {
                statItem.classList.add('connected');
            }

            const labelElement = trackedAddress.parentElement.querySelector('.stat-label');
            const secondaryElement = trackedAddress.parentElement.querySelector('.stat-secondary');
            if (labelElement) labelElement.textContent = 'Connected Address';
            if (secondaryElement) secondaryElement.textContent = 'Live data';
        }

        // Fetch and update gas data for the connected address
        await this.trackUserGas(address);
    }

    isValidEthereumAddress(address) {
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }

    async updateGasPrices() {
        // Rate limiting check
        const now = Date.now();
        if (now - this.lastApiCall < this.minApiInterval) {
            return;
        }

        try {
            const gasData = await this.fetchGasPrices();
            this.lastApiCall = now;

            if (gasData) {
                this.gasData = gasData;
                this.updateGasUI();
                this.updateGasHistory();
            }
        } catch (error) {
            // Silently handle API errors
        }
    }


        // Helper: read cached gas prices from localStorage
        loadGasPricesFromStorage() {
            try {
                const raw = localStorage.getItem('dripme_gas_prices');
                if (!raw) return null;
                const parsed = JSON.parse(raw);
                if (parsed && typeof parsed.fast === 'number' && typeof parsed.standard === 'number' && typeof parsed.safe === 'number') {
                    return { fast: parsed.fast, standard: parsed.standard, safe: parsed.safe };
                }
            } catch (e) {
                // Silently handle parsing errors
            }
            return null;
        }

        // Helper: read cached ETH price from localStorage
        loadETHPriceFromStorage() {
            try {
                const raw = localStorage.getItem('dripme_eth_price');
                if (!raw) return null;
                const parsed = JSON.parse(raw);
                if (parsed && typeof parsed.usd === 'number') {
                    return { usd: parsed.usd, change: parsed.change || 0 };
                }
            } catch (e) {
                // Silently handle parsing errors
            }
            return null;
        }


    async fetchGasPrices() {
        const apiUrl = 'https://api.etherscan.io/api?module=gastracker&action=gasoracle&apikey=T9RV3FGW573WX9YX45F1Z89MEMEUNQXUC7';

        const response = await fetch(apiUrl);
        const data = await response.json();

        if (data.status === '1' && data.result) {
            const gasData = {
                safe: parseFloat(data.result.SafeGasPrice),
                standard: parseFloat(data.result.ProposeGasPrice),
                fast: parseFloat(data.result.FastGasPrice)
            };

            // Persist latest gas prices for DevTools visibility (write-only)
            try {
                localStorage.setItem('dripme_gas_prices', JSON.stringify({
                    ...gasData,
                    timestamp: Date.now(),
                    lastUpdated: new Date().toISOString()
                }));
            } catch (e) {
                // Silently handle localStorage errors
            }
            return gasData;
        } else {
            throw new Error('Invalid response from Etherscan API');
        }
    }



    updateGasUI() {
        // Only update if we have valid gas data
        if (!this.gasData || this.gasData.fast === 0) {
            this.showGasLoading();
            return;
        }

        this.hideGasLoading();

        const currentGas = document.getElementById('current-gas');
        if (currentGas) {
            currentGas.textContent = `⚡ ${this.gasData.fast.toFixed(2)}`;
        }

        this.updateGasCard('fast-gas', this.gasData.fast, '~15 seconds');
        this.updateGasCard('standard-gas', this.gasData.standard, '~3 minutes');
        this.updateGasCard('safe-gas', this.gasData.safe, '~5 minutes');

        this.updateHeroPreview();
    }

    showGasLoading() {
        const currentGas = document.getElementById('current-gas');
        if (currentGas) {
            currentGas.textContent = 'Loading...';
        }

        // Show loading in gas cards
        ['fast-gas', 'standard-gas', 'safe-gas'].forEach(cardId => {
            const card = document.getElementById(cardId);
            if (card) {
                const priceElement = card.querySelector('.gas-price');
                if (priceElement) {
                    priceElement.textContent = 'Loading...';
                }
            }
        });

        // Show loading in hero section
        const heroSafeGas = document.getElementById('hero-safe-gas');
        const heroStandardGas = document.getElementById('hero-standard-gas');
        const heroFastGas = document.getElementById('hero-fast-gas');

        if (heroSafeGas) heroSafeGas.textContent = 'Loading...';
        if (heroStandardGas) heroStandardGas.textContent = 'Loading...';
        if (heroFastGas) heroFastGas.textContent = 'Loading...';
    }

    hideGasLoading() {
        // Loading states will be replaced by actual data in updateGasUI
    }

    showETHLoading() {
        const heroEthPrice = document.getElementById('hero-eth-price');
        if (heroEthPrice) {
            heroEthPrice.textContent = 'Loading...';
        }
    }



    loadSavedGasData() {
        try {
            const savedData = localStorage.getItem('dripme_gas_data');
            if (savedData) {
                const gasData = JSON.parse(savedData);
                console.log('📊 Loaded saved gas data:', gasData);

                // Merge with current data
                this.userGasData = { ...this.userGasData, ...gasData };

                // Update UI if data exists
                if (gasData.address && gasData.totalGasSpent > 0) {
                    this.updateUserGasUI();
                }
            }
        } catch (error) {
            console.warn('Failed to load saved gas data:', error);
            localStorage.removeItem('dripme_gas_data');
        }
    }

    saveGasData() {
        try {
            if (this.userGasData.address && this.userGasData.totalGasSpent > 0) {
                const dataToSave = {
                    ...this.userGasData,
                    timestamp: Date.now(),
                    lastUpdated: new Date().toISOString()
                };
                localStorage.setItem('dripme_gas_data', JSON.stringify(dataToSave));
                console.log('💾 Gas data saved to localStorage');
            }
        } catch (error) {
            console.error('Failed to save gas data:', error);
        }
    }

    clearSavedGasData() {
        localStorage.removeItem('dripme_gas_data');
    }

    updateHeroPreview() {
        const heroSafeGas = document.getElementById('hero-safe-gas');
        const heroStandardGas = document.getElementById('hero-standard-gas');
        const heroFastGas = document.getElementById('hero-fast-gas');
        const heroEthPrice = document.getElementById('hero-eth-price');

        if (heroSafeGas) heroSafeGas.textContent = `${this.gasData.safe.toFixed(2)} gwei`;
        if (heroStandardGas) heroStandardGas.textContent = `${this.gasData.standard.toFixed(2)} gwei`;
        if (heroFastGas) heroFastGas.textContent = `⚡ ${this.gasData.fast.toFixed(2)} gwei`;
        if (heroEthPrice && this.ethPrice > 0) {
            heroEthPrice.textContent = `$${this.ethPrice.toLocaleString()}`;
        }
    }

    updateGasCard(cardId, gasPrice, time) {
        const card = document.getElementById(cardId);
        if (!card) {
            return;
        }

        const priceElement = card.querySelector('.gas-price');
        const breakdownElement = card.querySelector('.gas-breakdown');
        const costElement = card.querySelector('.gas-cost');

        let displayPrice = gasPrice.toFixed(2);
        if (cardId === 'fast-gas') {
            displayPrice = `⚡ ${displayPrice}`;
        }

        if (priceElement) {
            priceElement.textContent = `${displayPrice} gwei`;
        }

        if (breakdownElement) {
            const baseFee = (gasPrice * 0.7).toFixed(2);
            const priorityFee = (gasPrice - (gasPrice * 0.7)).toFixed(2);
            breakdownElement.textContent = `Base: ${baseFee} | Priority: ${priorityFee}`;
        }

        if (costElement && this.ethPrice > 0) {
            const cost = this.estimateTransactionCost(21000, gasPrice);
            costElement.textContent = `$${cost.usd.toFixed(2)} | ${time}`;
        } else if (costElement) {
            costElement.textContent = `$-- | ${time}`;
        }
    }

    async updateETHPrice() {
        try {
            const apiUrl = 'https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd&include_24hr_change=true';

            const response = await fetch(apiUrl);
            const data = await response.json();

            if (data.ethereum) {
                this.ethPrice = data.ethereum.usd;
                this.ethChange = data.ethereum.usd_24h_change || 0;

                // Persist ETH price to localStorage
                try {
                    localStorage.setItem('dripme_eth_price', JSON.stringify({
                        usd: this.ethPrice,
                        change: this.ethChange,
                        timestamp: Date.now(),
                        lastUpdated: new Date().toISOString()
                    }));
                } catch (e) {
                    // Silently handle localStorage errors
                }

                this.updateETHPriceUI();
            }
        } catch (error) {
            // Silently handle ETH price API errors
        }
    }

    updateETHPriceUI() {
        const ethPrice = document.getElementById('eth-price');
        const ethChange = document.getElementById('eth-change');

        if (ethPrice && window.utils) {
            ethPrice.textContent = window.utils.formatCurrency(this.ethPrice);
        } else if (ethPrice) {
            ethPrice.textContent = `$${this.ethPrice.toLocaleString()}`;
        }

        if (ethChange && window.utils) {
            const changeText = window.utils.formatPercentage(this.ethChange);
            ethChange.textContent = changeText;

            ethChange.className = 'stat-change';
            if (this.ethChange > 0) {
                ethChange.classList.add('positive');
            } else if (this.ethChange < 0) {
                ethChange.classList.add('negative');
            }
        } else if (ethChange) {
            ethChange.textContent = `${this.ethChange > 0 ? '+' : ''}${this.ethChange.toFixed(2)}%`;
        }

        this.updateHeroPreview();
    }

    initChart() {
        if (this.chartInitialized) return;

        const chartCanvas = document.getElementById('gas-chart');
        if (!chartCanvas || !window.Chart) {
            return;
        }

        const ctx = chartCanvas.getContext('2d');

        const labels = [];
        const fastData = [];
        const standardData = [];
        const safeData = [];

        for (let i = 23; i >= 0; i--) {
            const time = new Date();
            time.setHours(time.getHours() - i);
            labels.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));

            fastData.push(Math.floor(Math.random() * 20) + 20);
            standardData.push(Math.floor(Math.random() * 15) + 15);
            safeData.push(Math.floor(Math.random() * 10) + 10);
        }

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Fast',
                        data: fastData,
                        borderColor: '#ff6b6b',
                        backgroundColor: 'rgba(255, 107, 107, 0.1)',
                        tension: 0.4,
                        fill: false
                    },
                    {
                        label: 'Standard',
                        data: standardData,
                        borderColor: '#4ecdc4',
                        backgroundColor: 'rgba(78, 205, 196, 0.1)',
                        tension: 0.4,
                        fill: false
                    },
                    {
                        label: 'Safe',
                        data: safeData,
                        borderColor: '#45b7d1',
                        backgroundColor: 'rgba(69, 183, 209, 0.1)',
                        tension: 0.4,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: 'var(--text-color)',
                            usePointStyle: true
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'var(--border-color)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)'
                        }
                    },
                    y: {
                        grid: {
                            color: 'var(--border-color)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)',
                            callback: function(value) {
                                return value + ' gwei';
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        this.chartInitialized = true;
        console.log('📊 Gas price chart initialized');
    }

    updateGasHistory() {
        if (!this.chart) return;

        const now = new Date();
        const timeLabel = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

        this.chart.data.labels.push(timeLabel);
        this.chart.data.datasets[0].data.push(this.gasData.fast);
        this.chart.data.datasets[1].data.push(this.gasData.standard);
        this.chart.data.datasets[2].data.push(this.gasData.safe);

        if (this.chart.data.labels.length > 24) {
            this.chart.data.labels.shift();
            this.chart.data.datasets.forEach(dataset => {
                dataset.data.shift();
            });
        }

        this.chart.update('none');
    }

    getCurrentGasPrices() {
        return { ...this.gasData };
    }

    estimateTransactionCost(gasLimit = 21000, gasPrice = null) {
        const price = gasPrice || this.gasData.standard;
        const costInWei = gasLimit * price * 1e9;
        const costInETH = costInWei / 1e18;
        const costInUSD = costInETH * this.ethPrice;

        return {
            eth: costInETH,
            usd: costInUSD,
            gwei: price
        };
    }

    async trackUserGas(address) {
        this.showLoading();
        this.hideError();

        // Show loading state in the address display
        const trackedAddress = document.getElementById('tracked-address');
        if (trackedAddress) {
            const secondaryElement = trackedAddress.parentElement.querySelector('.stat-secondary');
            if (secondaryElement) {
                secondaryElement.textContent = 'Fetching data...';
            }
        }

        try {
            const transactions = await this.fetchUserTransactions(address);
            if (transactions && transactions.length > 0) {
                this.calculateGasStats(transactions, address);
                this.updateUserGasUI();
                this.showGasStats();
            } else {
                this.showError('No transactions found for this address');
            }
        } catch (error) {
            // Check if it's a "no transactions found" case vs actual error
            if (error.message && error.message.includes('No transactions found')) {
                this.handleNoTransactionsFound();
            } else {
                // this.showError('Failed to fetch transaction data. Please try again.');
            }
        } finally {
            this.hideLoading();
        }
    }

    async fetchUserTransactions(address) {
        const apiKey = 'T9RV3FGW573WX9YX45F1Z89MEMEUNQXUC7';
        const apiUrl = `https://api.etherscan.io/api?module=account&action=txlist&address=${address}&startblock=0&endblock=********&sort=asc&apikey=${apiKey}`;

        try {
            const response = await fetch(apiUrl);
            const data = await response.json();

            if (data.status === '1' && data.result) {
                if (Array.isArray(data.result) && data.result.length === 0) {
                    throw new Error('No transactions found for this address');
                }
                return data.result;
            } else {
                throw new Error(data.message || 'Failed to fetch transactions');
            }
        } catch (error) {
            throw error;
        }
    }

    calculateGasStats(transactions, address) {
        let totalGasSpent = 0;
        let totalGasUsed = 0;
        let totalGasPrice = 0;
        let failedCount = 0;

        transactions.forEach(tx => {
            const gasUsed = parseInt(tx.gasUsed);
            const gasPrice = parseInt(tx.gasPrice);
            const isError = tx.isError === '1';

            if (isError) {
                failedCount++;
            }

            const gasCostWei = gasUsed * gasPrice;
            const gasCostETH = gasCostWei / 1e18;

            totalGasSpent += gasCostETH;
            totalGasUsed += gasUsed;
            totalGasPrice += gasPrice;
        });

        this.userGasData = {
            totalGasSpent: totalGasSpent,
            totalTransactions: transactions.length,
            totalGasUsed: totalGasUsed,
            failedTransactions: failedCount,
            avgGasPrice: totalGasPrice / transactions.length / 1e9,
            address: address
        };

        // Save the calculated gas data to localStorage
        this.saveGasData();
    }



    updateUserGasUI() {
        const totalGasEth = document.getElementById('total-gas-eth');
        const totalGasUsd = document.getElementById('total-gas-usd');
        const totalTransactions = document.getElementById('total-transactions');
        const avgGasPrice = document.getElementById('avg-gas-price');
        const totalGasUsed = document.getElementById('total-gas-used');
        const failedTransactions = document.getElementById('failed-transactions');
        const trackedAddress = document.getElementById('tracked-address');

        if (totalGasEth) {
            const gasSpent = this.userGasData.totalGasSpent;
            if (gasSpent > 0) {
                totalGasEth.textContent = `Ξ${gasSpent.toFixed(3)}`;
            } else {
                totalGasEth.textContent = 'No gas spent';
            }
        }

        if (totalGasUsd && this.ethPrice > 0) {
            const gasSpent = this.userGasData.totalGasSpent;
            if (gasSpent > 0) {
                const usdValue = gasSpent * this.ethPrice;
                totalGasUsd.textContent = `$${usdValue.toFixed(2)}`;
            } else {
                totalGasUsd.textContent = '$0.00';
            }
        } else if (totalGasUsd) {
            totalGasUsd.textContent = '$-';
        }

        if (totalTransactions) {
            totalTransactions.textContent = this.userGasData.totalTransactions.toLocaleString();
        }

        if (avgGasPrice) {
            if (this.userGasData.avgGasPrice > 0) {
                avgGasPrice.textContent = `Avg: ${this.userGasData.avgGasPrice.toFixed(2)} Gwei`;
            } else {
                avgGasPrice.textContent = 'No transactions';
            }
        }

        if (totalGasUsed) {
            totalGasUsed.textContent = this.userGasData.totalGasUsed.toLocaleString();
        }

        if (failedTransactions) {
            failedTransactions.textContent = `${this.userGasData.failedTransactions} failed`;
        }

        if (trackedAddress) {
            if (this.userGasData.address === '0xF0f35…07708') {
                trackedAddress.textContent = 'No account connected';
                trackedAddress.classList.remove('connected');

                const statItem = trackedAddress.closest('.stat-item');
                if (statItem) {
                    statItem.classList.remove('connected');
                }

                const labelText = trackedAddress.parentElement.querySelector('.stat-label');
                const secondaryText = trackedAddress.parentElement.querySelector('.stat-secondary');
                if (labelText) labelText.textContent = 'Example Address';
                if (secondaryText) {
                    secondaryText.textContent = `using ${this.userGasData.address} as an example`;
                }
            } else {
                trackedAddress.textContent = this.formatAddress(this.userGasData.address);
                trackedAddress.classList.add('connected');

                const statItem = trackedAddress.closest('.stat-item');
                if (statItem) {
                    statItem.classList.add('connected');
                }

                const labelText = trackedAddress.parentElement.querySelector('.stat-label');
                const secondaryText = trackedAddress.parentElement.querySelector('.stat-secondary');
                if (labelText) labelText.textContent = 'Connected Address';
                if (secondaryText) {
                    secondaryText.textContent = 'Live data';
                }
            }
        }
    }

    formatAddress(address) {
        if (address.length <= 10) return address;
        return `${address.slice(0, 6)}…${address.slice(-6)}`;
    }

    showGasStats() {
        console.log('👁️ Showing gas stats...');
        const gasStats = document.getElementById('gas-stats');
        console.log('🔍 Gas stats element found:', !!gasStats);
        if (gasStats) {
            gasStats.classList.remove('hidden');
            console.log('✅ Gas stats shown');
        } else {
            console.warn('⚠️ Gas stats element not found');
        }
    }

    hideGasStats() {
        const gasStats = document.getElementById('gas-stats');
        if (gasStats) {
            gasStats.classList.add('hidden');
        }
    }

    showLoading() {
        const loading = document.getElementById('loading-gas');
        if (loading) {
            loading.classList.remove('hidden');
        }
    }

    hideLoading() {
        const loading = document.getElementById('loading-gas');
        if (loading) {
            loading.classList.add('hidden');
        }
    }

    showError(message) {
        const error = document.getElementById('gas-error');
        if (error) {
            const errorText = error.querySelector('span:last-child');
            if (errorText) {
                errorText.textContent = message;
            }
            error.classList.remove('hidden');
        }
    }

    hideError() {
        const error = document.getElementById('gas-error');
        if (error) {
            error.classList.add('hidden');
        }
    }

    handleNoTransactionsFound() {
        // Set user gas data to zero values
        this.userGasData = {
            totalGasSpent: 0,
            totalTransactions: 0,
            totalGasUsed: 0,
            failedTransactions: 0,
            avgGasPrice: 0,
            address: window.feesWTFApp?.walletAddress || 'unknown'
        };

        // Update UI with zero values (no error message)
        this.updateUserGasUI();
        this.showGasStats();

        // Save the zero data
        this.saveGasData();
    }

    showNoDataMessage() {
        // Update UI to show no data found message
        const totalGasEth = document.getElementById('total-gas-eth');
        const totalGasUsd = document.getElementById('total-gas-usd');
        const totalTransactions = document.getElementById('total-transactions');
        const avgGasPrice = document.getElementById('avg-gas-price');
        const totalGasUsed = document.getElementById('total-gas-used');
        const failedTransactions = document.getElementById('failed-transactions');

        if (totalGasEth) totalGasEth.textContent = 'No transactions found';
        if (totalGasUsd) totalGasUsd.textContent = '$0.00';
        if (totalTransactions) totalTransactions.textContent = '0';
        if (avgGasPrice) avgGasPrice.textContent = 'No data';
        if (totalGasUsed) totalGasUsed.textContent = '0';
        if (failedTransactions) failedTransactions.textContent = '0 failed';

        // Show gas stats section with no data message
        const gasStats = document.getElementById('gas-stats');
        if (gasStats) {
            gasStats.classList.remove('hidden');
        }
    }
}

window.gasTracker = new GasTracker();

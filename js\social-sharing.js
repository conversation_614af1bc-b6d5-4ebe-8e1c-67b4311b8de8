class SocialSharingManager {
    constructor() {
        this.hasShared = false;
        this.userGasData = null;
        this.twitterWindow = null;
        this.sharingInProgress = false;
        this.verificationTimeout = null;

        // 24-hour expiration for sharing (in milliseconds)
        this.SHARING_EXPIRATION = 24 * 60 * 60 * 1000;

        // Debug: Log current localStorage state
        console.log('🔍 SocialSharingManager initialized. Current localStorage:', {
            sharingData: this.getAllSharingData()
        });

        this.init();
    }

    // Get all sharing data for debugging
    getAllSharingData() {
        try {
            const sharingData = localStorage.getItem('dripme_sharing_data');
            return sharingData ? JSON.parse(sharingData) : {};
        } catch (e) {
            console.error('Error parsing sharing data:', e);
            return {};
        }
    }

    // Store sharing data per address with timestamp
    storeSharingData(address, timestamp = Date.now()) {
        try {
            const sharingData = this.getAllSharingData();
            sharingData[address.toLowerCase()] = {
                timestamp,
                shared: true,
                lastUpdated: new Date().toISOString()
            };
            localStorage.setItem('dripme_sharing_data', JSON.stringify(sharingData));
            console.log('💾 Sharing data stored for address:', address.toLowerCase());
        } catch (e) {
            console.error('Error storing sharing data:', e);
        }
    }

    // Check if address has valid (non-expired) sharing
    hasValidSharing(address) {
        if (!address) return false;

        const sharingData = this.getAllSharingData();
        const addressData = sharingData[address.toLowerCase()];

        if (!addressData || !addressData.shared) {
            console.log('� No sharing data found for address:', address.toLowerCase());
            return false;
        }

        const timeSinceSharing = Date.now() - addressData.timestamp;
        const isExpired = timeSinceSharing > this.SHARING_EXPIRATION;

        console.log('⏰ Sharing validation for', address.toLowerCase(), {
            timeSinceSharing: Math.round(timeSinceSharing / (1000 * 60 * 60)) + ' hours',
            isExpired,
            sharedAt: new Date(addressData.timestamp).toISOString()
        });

        return !isExpired;
    }

    // Clear expired sharing data
    cleanupExpiredSharing() {
        try {
            const sharingData = this.getAllSharingData();
            const now = Date.now();
            let cleaned = false;

            Object.keys(sharingData).forEach(address => {
                const data = sharingData[address];
                if (now - data.timestamp > this.SHARING_EXPIRATION) {
                    delete sharingData[address];
                    cleaned = true;
                    console.log('🧹 Cleaned expired sharing data for:', address);
                }
            });

            if (cleaned) {
                localStorage.setItem('dripme_sharing_data', JSON.stringify(sharingData));
            }
        } catch (e) {
            console.error('Error cleaning expired sharing data:', e);
        }
    }

    init() {
        console.log('🚀 Initializing SocialSharingManager...');
        console.log('🌐 Current environment:', {
            hostname: window.location.hostname,
            pathname: window.location.pathname,
            port: window.location.port,
            basePath: this.getBasePath()
        });

        this.setupSharingModal();
        this.bindEvents();
        this.cleanupExpiredSharing();

        console.log('✅ SocialSharingManager initialization complete');
    }

    // Get the correct base path for cPanel and other environments
    getBasePath() {
        const currentPath = window.location.pathname;
        
        // For cPanel subdirectory
        if (currentPath.includes('/top/')) {
            return '/top/';
        }
        
        // For cPanel subdomain or root
        return '/';
    }

    // Get the correct image path for different environments
    getImagePath(imageName) {
        const basePath = this.getBasePath();
        return `${basePath}images/${imageName}`;
    }

    // Simplified - no image generation needed

    // Image generation removed - not needed

    setupSharingModal() {
        console.log('🔧 Setting up sharing modal...');

        // Ensure DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupSharingModal());
            return;
        }

        // Create sharing modal HTML if it doesn't exist
        if (!document.getElementById('sharing-modal')) {
            console.log('📝 Creating modal HTML...');

            // Get multiple possible image paths for cPanel compatibility
            const imagePaths = [
                this.getImagePath('driplogo.PNG'),
                './images/driplogo.PNG',
                'images/driplogo.PNG'
            ];

            let logoSrc = imagePaths[0];

            // Test which image path works
            const testImage = new Image();
            testImage.onerror = () => {
                console.log(`❌ Failed to load ${logoSrc}, trying fallback`);
                logoSrc = './images/driplogo.PNG';
            };
            testImage.src = logoSrc;

            const modalHTML = `
                <div id="sharing-modal" class="modal-overlay hidden">
                    <div class="modal-content sharing-modal">
                        <div class="modal-header">
                            <h2>🚀 Share Your Gas Journey</h2>
                            <button id="close-sharing-modal" class="close-btn">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="sharing-content">
                                <div class="drip-logo-3d">
                                    <img src="${logoSrc}" alt="DripMe" class="logo-3d" onerror="this.style.display='none'">
                                </div>
                                <h3>Share to unlock your $DRIP allocation! 💧</h3>
                                <p>Tweet your gas tracking results to see your allocation amount.</p>

                                <div class="tweet-preview">
                                    <div class="tweet-header">
                                        <div class="twitter-icon">🐦</div>
                                        <span>Preview your tweet:</span>
                                    </div>
                                    <div class="tweet-content" id="tweet-preview-text">
                                        Just tracked my on-chain gas spend ⛽ Burned [CALCULATING...] ETH total & unlocked a $DRIP stimmy 💧 @DripMe_xyz Check yours: https://dripme.xyz/
                                    </div>
                                </div>

                                <div class="sharing-actions">
                                    <button id="share-twitter-btn" class="share-btn twitter-btn">
                                        <span class="btn-icon">🐦</span>
                                        Share on Twitter
                                    </button>
                                    <p class="sharing-note">After sharing, return here to see your allocation!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            try {
                document.body.insertAdjacentHTML('beforeend', modalHTML);
                console.log('✅ Modal HTML created and added to DOM');

                // Verify modal was created
                const modal = document.getElementById('sharing-modal');
                if (!modal) {
                    console.error('❌ Modal creation failed - element not found after insertion');
                } else {
                    console.log('✅ Modal element verified in DOM');
                }
            } catch (error) {
                console.error('❌ Error creating modal:', error);
            }
        } else {
            console.log('ℹ️ Modal already exists in DOM');
        }
    }

    bindEvents() {
        console.log('🔗 Binding modal events...');

        // Use a slight delay to ensure DOM is ready
        setTimeout(() => {
            // Close modal events
            const closeBtn = document.getElementById('close-sharing-modal');
            const modal = document.getElementById('sharing-modal');

            console.log('🔍 Modal elements found:', {
                closeBtn: !!closeBtn,
                modal: !!modal
            });

            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.hideSharingModal());
                console.log('✅ Close button event bound');
            }

            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.hideSharingModal();
                    }
                });
                console.log('✅ Modal overlay event bound');
            }

            // Twitter share button
            const shareBtn = document.getElementById('share-twitter-btn');
            if (shareBtn) {
                shareBtn.addEventListener('click', () => this.shareToTwitter());
                console.log('✅ Share button event bound');
            } else {
                console.log('⚠️ Share button not found');
            }
        }, 100);

        // Listen for window focus to detect return from Twitter
        window.addEventListener('focus', () => {
            if (this.hasShared) {
                this.handleReturnFromTwitter();
            }
        });
    }

    showSharingModal(gasData) {
        console.log('📱 Showing sharing interface with gas data:', gasData);
        this.userGasData = gasData;

        // Instead of showing a modal, replace the allocation content with sharing interface
        this.showSharingInterface(gasData);
    }

    showSharingInterface(gasData) {
        console.log('📱 showSharingInterface called with gasData:', gasData);

        const allocationSection = document.querySelector('.feature-card');
        console.log('🔍 Found allocation section:', !!allocationSection);

        if (!allocationSection) {
            console.error('❌ Allocation section not found');
            return;
        }

        const gasAmount = gasData.totalGasSpent.toFixed(3);
        const tweetText = `Just tracked my on-chain gas spend ⛽ Burned ${gasAmount} ETH total & unlocked a $DRIP stimmy 💧 @DripMe_xyz Check yours: https://dripclaim.xyz/`;

        // Create sharing interface HTML
        const sharingHTML = `
            <div id="sharing-interface" class="sharing-interface">
                <div class="sharing-header" style="text-align: center;">
                    <div class="drip-logo-small" style="display: flex; justify-content: center; margin-bottom: 16px;">
                        <img src="${this.getImagePath('driplogo.PNG')}" alt="DripMe" style="width: 50px; height: 50px; border-radius: 50%;" onerror="this.style.display='none'">
                    </div>
                    <h3 style="margin: 0; color: #ffffff;">🚀 Share to unlock your allocation!</h3>
                </div>

                <div class="tweet-compose" style="
                    background: rgba(0, 0, 0, 0.4);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    padding: 20px;
                    margin: 20px 0;
                ">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                        <div style="color: #1da1f2; font-size: 1.2rem;">🐦</div>
                        <span style="color: #ffffff; font-weight: 600;">Your Tweet:</span>
                    </div>

                    <div style="
                        background: rgba(0, 0, 0, 0.3);
                        border: 1px solid rgba(255, 255, 255, 0.08);
                        border-radius: 8px;
                        padding: 16px;
                        color: #f0f0f0;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        line-height: 1.5;
                        font-size: 0.9rem;
                        margin-bottom: 20px;
                    " id="tweet-text-display">${tweetText}</div>

                    <div style="display: flex; gap: 12px; justify-content: center;">
                        <button id="share-twitter-inline" style="
                            background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
                            color: white;
                            border: none;
                            padding: 14px 28px;
                            border-radius: 12px;
                            font-size: 1rem;
                            font-weight: 600;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            transition: all 0.2s ease;
                            box-shadow: 0 8px 20px rgba(29, 161, 242, 0.3);
                            border: 1px solid rgba(29, 161, 242, 0.2);
                        ">
                            <span>🐦</span>
                            Share on Twitter
                        </button>

                        <button id="copy-tweet-text" style="
                            background: rgba(255, 255, 255, 0.1);
                            color: #ffffff;
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            padding: 14px 20px;
                            border-radius: 12px;
                            font-size: 0.9rem;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.2s ease;
                        ">
                            📋 Copy Text
                        </button>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 16px;">
                    <p style="margin: 0; font-size: 0.85rem; opacity: 0.7; color: #b0b0b0;">
                        Your allocation will unlock automatically after sharing
                    </p>
                </div>
            </div>
        `;

        // Make sure the allocation section is visible
        allocationSection.style.display = 'block';

        // Replace the allocation content with sharing interface
        allocationSection.innerHTML = sharingHTML;

        console.log('✅ Sharing interface HTML inserted');

        // Bind events for the new interface
        this.bindSharingInterfaceEvents(tweetText);
    }

    hideSharingModal() {
        // No longer needed with inline interface
        console.log('ℹ️ hideSharingModal called but using inline interface');
    }

    bindSharingInterfaceEvents(tweetText) {
        // Share button
        const shareBtn = document.getElementById('share-twitter-inline');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.shareToTwitterInline(tweetText));
        }

        // Copy button
        const copyBtn = document.getElementById('copy-tweet-text');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => this.copyTweetText(tweetText));
        }
    }

    async shareToTwitterInline(tweetText) {
        if (!this.userGasData || this.sharingInProgress) return;

        this.sharingInProgress = true;

        try {
            // Encode the tweet text for URL
            const encodedText = encodeURIComponent(tweetText);

            // Create Twitter share URL
            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodedText}`;

            // Open Twitter in new tab
            console.log('🐦 Opening Twitter share...');
            window.open(twitterUrl, '_blank');

            // Automatically unlock allocation after sharing (simplified)
            setTimeout(() => {
                this.confirmSharingCompleted();
            }, 3000); // 3 second delay to allow Twitter to open

        } catch (error) {
            console.error('Error sharing to Twitter:', error);
            this.sharingInProgress = false;
            this.showError('Failed to open Twitter sharing.');
        }
    }

    copyTweetText(tweetText) {
        navigator.clipboard.writeText(tweetText).then(() => {
            // Show copy success feedback
            const copyBtn = document.getElementById('copy-tweet-text');
            if (copyBtn) {
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '✅ Copied!';
                copyBtn.style.background = 'rgba(34, 197, 94, 0.2)';
                copyBtn.style.borderColor = 'rgba(34, 197, 94, 0.4)';
                copyBtn.style.color = '#22c55e';

                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.style.background = 'rgba(255, 255, 255, 0.1)';
                    copyBtn.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    copyBtn.style.color = '#ffffff';
                }, 2000);
            }
        }).catch(err => {
            console.error('Failed to copy text:', err);
        });
    }

    // Verification interface removed - using automatic verification

    showPopupBlockedMessage(twitterUrl) {
        const popupBlockedHTML = `
            <div id="popup-blocked" class="sharing-status">
                <div style="font-size: 2rem; margin-bottom: 16px;">🚫</div>
                <h3 style="color: #ef4444; margin: 0 0 12px 0; font-size: 1.2rem;">Popup Blocked</h3>
                <p style="margin: 0 0 20px 0; opacity: 0.85; color: #e0e0e0;">
                    Your browser blocked the Twitter popup. Please allow popups for this site or click the link below.
                </p>
                <div style="display: flex; flex-direction: column; gap: 12px; align-items: center;">
                    <a href="${twitterUrl}" target="_blank" style="
                        background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
                        color: white;
                        text-decoration: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        transition: all 0.2s ease;
                        border: 1px solid rgba(29, 161, 242, 0.2);
                    ">🐦 Open Twitter Manually</a>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
                        background: rgba(239, 68, 68, 0.1);
                        color: #ef4444;
                        border: 1px solid rgba(239, 68, 68, 0.3);
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-size: 0.8rem;
                        cursor: pointer;
                    ">Close</button>
                </div>
            </div>
        `;

        const allocationSection = document.querySelector('.airdrop-section');
        if (allocationSection) {
            allocationSection.insertAdjacentHTML('beforeend', popupBlockedHTML);
        }
    }

    startSharingVerification() {
        console.log('🔍 Starting sharing verification...');

        // Clear any existing verification timeout
        if (this.verificationTimeout) {
            clearTimeout(this.verificationTimeout);
        }

        // Set a timeout for verification (5 minutes max)
        this.verificationTimeout = setTimeout(() => {
            console.log('⏰ Sharing verification timed out');
            this.handleSharingCancelled();
        }, 5 * 60 * 1000);

        // Monitor the Twitter window
        this.monitorTwitterWindow();
    }

    monitorTwitterWindow() {
        if (!this.twitterWindow) {
            this.handleSharingCancelled();
            return;
        }

        // Check if window is closed
        if (this.twitterWindow.closed) {
            console.log('🪟 Twitter window closed');

            // Give a brief delay to allow for any final processing
            setTimeout(() => {
                this.handleSharingCancelled();
            }, 2000);
            return;
        }

        // Continue monitoring
        setTimeout(() => {
            this.monitorTwitterWindow();
        }, 1000);
    }

    handleSharingCancelled() {
        console.log('❌ Sharing was cancelled or incomplete');

        this.sharingInProgress = false;
        this.twitterWindow = null;

        if (this.verificationTimeout) {
            clearTimeout(this.verificationTimeout);
            this.verificationTimeout = null;
        }

        // Show cancellation message in the allocation box
        this.showCancellationInterface();
    }

    showCancellationInterface() {
        const allocationSection = document.querySelector('.feature-card');
        if (!allocationSection) return;

        const cancellationHTML = `
            <div id="sharing-cancelled-interface" class="cancellation-interface">
                <div style="text-align: center; padding: 40px 20px;">
                    <div style="font-size: 3rem; margin-bottom: 20px;">❌</div>
                    <h3 style="color: #ef4444; margin: 0 0 16px 0; font-size: 1.4rem;">Sharing Cancelled</h3>
                    <p style="margin: 0 0 24px 0; opacity: 0.85; color: #e0e0e0; line-height: 1.6;">
                        You must complete the Twitter sharing to see your $DRIP allocation.
                    </p>

                    <button onclick="window.socialSharingManager.showSharingInterface(window.socialSharingManager.userGasData)" style="
                        background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
                        color: white;
                        border: none;
                        padding: 16px 32px;
                        border-radius: 12px;
                        font-size: 1rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border: 1px solid rgba(29, 161, 242, 0.2);
                        box-shadow: 0 8px 20px rgba(29, 161, 242, 0.3);
                    ">🔄 Try Again</button>
                </div>
            </div>
        `;

        allocationSection.innerHTML = cancellationHTML;
    }

    showCancellationMessage() {
        const cancellationHTML = `
            <div id="sharing-cancelled" class="sharing-status">
                <div class="cancellation-content">
                    <div style="font-size: 2rem; margin-bottom: 16px;">❌</div>
                    <h3 style="color: #ef4444; margin: 0 0 12px 0; font-size: 1.2rem;">Sharing Cancelled</h3>
                    <p style="margin: 0 0 20px 0; opacity: 0.85; color: #e0e0e0;">You must complete the Twitter sharing to see your allocation.</p>
                    <button onclick="window.socialSharingManager.showSharingModal(window.socialSharingManager.userGasData)" style="
                        background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border: 1px solid rgba(29, 161, 242, 0.2);
                    ">Try Again</button>
                </div>
            </div>
        `;

        const allocationSection = document.querySelector('.airdrop-section');
        if (allocationSection) {
            allocationSection.insertAdjacentHTML('beforeend', cancellationHTML);

            // Remove cancellation message after 10 seconds
            setTimeout(() => {
                const cancelElement = document.getElementById('sharing-cancelled');
                if (cancelElement) {
                    cancelElement.remove();
                }
            }, 10000);
        }
    }

    showWaitingMessage() {
        const waitingHTML = `
            <div id="sharing-waiting" class="sharing-status">
                <div class="waiting-content">
                    <div class="spinner" style="
                        width: 32px;
                        height: 32px;
                        border: 3px solid rgba(255,255,255,0.2);
                        border-top: 3px solid #1da1f2;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 16px;
                    "></div>
                    <h3 style="color: #ffffff; margin: 0 0 12px 0; font-size: 1.2rem;">🐦 Complete your tweet...</h3>
                    <p style="margin: 0 0 20px 0; opacity: 0.85; color: #e0e0e0;">
                        Post your tweet on Twitter, then return here to verify and unlock your allocation.
                    </p>
                    <button id="check-sharing-btn" style="
                        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border: 1px solid rgba(34, 197, 94, 0.2);
                    ">I've posted the tweet!</button>
                </div>
            </div>
        `;
        
        // Insert waiting message in allocation section
        const allocationSection = document.querySelector('.airdrop-section');
        if (allocationSection) {
            allocationSection.insertAdjacentHTML('beforeend', waitingHTML);
        }

        // Bind check button
        const checkBtn = document.getElementById('check-sharing-btn');
        if (checkBtn) {
            checkBtn.addEventListener('click', () => this.handleReturnFromTwitter());
        }
    }

    handleReturnFromTwitter() {
        console.log('🔄 Handling return from Twitter...');

        // This method is now called when we have confirmation of sharing
        // We'll implement a more secure verification process
        this.showVerificationMessage();
    }

    // Enhanced verification with manual confirmation
    confirmSharingCompleted() {
        if (!this.userGasData || !this.sharingInProgress) {
            console.log('❌ No sharing in progress or no gas data');
            return;
        }

        console.log('✅ User confirmed sharing completion');

        // Store the sharing data with current timestamp
        this.storeSharingData(this.userGasData.address);

        // Clear verification timeout
        if (this.verificationTimeout) {
            clearTimeout(this.verificationTimeout);
            this.verificationTimeout = null;
        }

        // Reset sharing state
        this.sharingInProgress = false;
        this.twitterWindow = null;

        // Unlock allocation
        this.unlockAllocation();
    }

    showVerificationMessage() {
        const verificationHTML = `
            <div class="verification-content">
                <div style="font-size: 2rem; margin-bottom: 16px;">🔍</div>
                <h3 style="color: #ffffff; margin: 0 0 12px 0; font-size: 1.2rem;">Verify Your Tweet</h3>
                <p style="margin: 0 0 20px 0; opacity: 0.85; color: #e0e0e0;">
                    Please confirm that you successfully posted the tweet to unlock your allocation.
                </p>
                <div style="margin-bottom: 20px; padding: 12px; background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px;">
                    <p style="margin: 0; font-size: 0.85rem; color: #ffc107;">
                        ⚠️ Only click "Yes, I shared!" if you actually posted the tweet.
                        False confirmations may result in allocation restrictions.
                    </p>
                </div>
                <div style="display: flex; gap: 12px; justify-content: center;">
                    <button id="confirm-shared-btn" style="
                        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border: 1px solid rgba(34, 197, 94, 0.2);
                    ">Yes, I shared!</button>
                    <button id="cancel-shared-btn" style="
                        background: rgba(239, 68, 68, 0.1);
                        color: #ef4444;
                        border: 1px solid rgba(239, 68, 68, 0.3);
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    ">No, I cancelled</button>
                </div>
            </div>
        `;

        // Replace waiting message with verification
        const waitingElement = document.getElementById('sharing-waiting');
        if (waitingElement) {
            waitingElement.innerHTML = verificationHTML;
        }

        // Bind verification buttons
        const confirmBtn = document.getElementById('confirm-shared-btn');
        const cancelBtn = document.getElementById('cancel-shared-btn');

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.confirmSharingCompleted());
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.handleSharingCancelled());
        }
    }

    unlockAllocation() {
        console.log('🔓 Unlocking allocation for address:', this.userGasData?.address);

        // Show success message in the allocation box
        this.showAllocationUnlocked();

        // Trigger allocation display
        if (window.airdropManager) {
            window.airdropManager.showAllocation(this.userGasData);
        }
    }

    showAllocationUnlocked() {
        const allocationSection = document.querySelector('.feature-card');
        if (!allocationSection) return;

        const successHTML = `
            <div id="allocation-unlocked" class="allocation-unlocked">
                <div style="text-align: center; padding: 40px 20px;">
                    <div style="font-size: 3rem; margin-bottom: 20px; animation: bounce 1s ease-in-out;">🎉</div>
                    <h3 style="color: #22c55e; margin: 0 0 16px 0; font-size: 1.6rem;">Allocation Unlocked!</h3>
                    <p style="margin: 0 0 24px 0; opacity: 0.9; color: #e0e0e0; font-size: 1.1rem;">
                        Thanks for sharing! Your $DRIP allocation is now being calculated...
                    </p>

                    <div style="
                        background: linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
                        border: 1px solid rgba(34, 197, 94, 0.3);
                        border-radius: 12px;
                        padding: 24px;
                        margin: 20px 0;
                    ">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 3px solid rgba(34, 197, 94, 0.2);
                            border-top: 3px solid #22c55e;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 16px;
                        "></div>
                        <p style="margin: 0; color: #22c55e; font-weight: 600;">Loading your allocation...</p>
                    </div>
                </div>
            </div>
        `;

        allocationSection.innerHTML = successHTML;

        // After a short delay, show the actual allocation
        setTimeout(() => {
            if (window.airdropManager) {
                // This will replace the success message with the actual allocation
                window.airdropManager.displayAllocationInBox(this.userGasData);
            }
        }, 2000);
    }

    showUnlockSuccess() {
        const successHTML = `
            <div class="unlock-success" style="
                background: linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
                border: 1px solid rgba(34, 197, 94, 0.3);
                border-radius: 12px;
                padding: 24px;
                margin: 20px 0;
                text-align: center;
                color: white;
                backdrop-filter: blur(10px);
                animation: slideIn 0.5s ease-out;
            ">
                <div class="success-animation" style="font-size: 2rem; margin-bottom: 12px;">🎉</div>
                <h3 style="color: #22c55e; margin: 0 0 8px 0; font-size: 1.2rem;">Allocation Unlocked!</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.9rem;">Thanks for sharing! Your $DRIP allocation is now visible below.</p>
            </div>
        `;

        const allocationSection = document.querySelector('.airdrop-section');
        if (allocationSection) {
            allocationSection.insertAdjacentHTML('afterbegin', successHTML);

            // Remove success message after 4 seconds
            setTimeout(() => {
                const successElement = document.querySelector('.unlock-success');
                if (successElement) {
                    successElement.style.opacity = '0';
                    successElement.style.transform = 'translateY(-20px)';
                    setTimeout(() => successElement.remove(), 300);
                }
            }, 4000);
        }
    }

    checkSharingRequired(gasData) {
        if (!gasData || !gasData.address) {
            console.log('❌ No gas data or address provided');
            return true;
        }

        // Clean up expired sharing data first
        this.cleanupExpiredSharing();

        // Check if this address has valid (non-expired) sharing
        const hasValidSharing = this.hasValidSharing(gasData.address);

        console.log('🔍 Checking sharing requirement for', gasData.address.toLowerCase(), {
            hasValidSharing,
            sharingRequired: !hasValidSharing
        });

        return !hasValidSharing; // Return true if sharing is required
    }

    requireSharing(gasData) {
        console.log('🔍 requireSharing called with gasData:', gasData);

        if (this.checkSharingRequired(gasData)) {
            console.log('🚀 Showing sharing interface - sharing required');
            this.showSharingModal(gasData);
            return true;
        }
        console.log('⏭️ Skipping sharing interface - valid sharing found');
        return false;
    }

    // Add error display method
    showError(message) {
        const errorHTML = `
            <div id="sharing-error" class="sharing-status">
                <div style="font-size: 2rem; margin-bottom: 16px;">⚠️</div>
                <h3 style="color: #ef4444; margin: 0 0 12px 0; font-size: 1.2rem;">Error</h3>
                <p style="margin: 0; opacity: 0.85; color: #e0e0e0;">${message}</p>
            </div>
        `;

        const allocationSection = document.querySelector('.airdrop-section');
        if (allocationSection) {
            // Remove any existing error messages
            const existingError = document.getElementById('sharing-error');
            if (existingError) {
                existingError.remove();
            }

            allocationSection.insertAdjacentHTML('beforeend', errorHTML);

            // Remove error message after 5 seconds
            setTimeout(() => {
                const errorElement = document.getElementById('sharing-error');
                if (errorElement) {
                    errorElement.remove();
                }
            }, 5000);
        }
    }
}

// Initialize social sharing manager with cPanel compatibility
console.log('🚀 Initializing SocialSharingManager...');

function initializeSocialSharing() {
    try {
        if (!window.socialSharingManager) {
            window.socialSharingManager = new SocialSharingManager();
            console.log('✅ SocialSharingManager initialized:', window.socialSharingManager);
        }
    } catch (error) {
        console.error('❌ Error initializing SocialSharingManager:', error);

        // Retry after a delay for cPanel environments
        setTimeout(() => {
            try {
                if (!window.socialSharingManager) {
                    window.socialSharingManager = new SocialSharingManager();
                    console.log('✅ SocialSharingManager initialized on retry');
                }
            } catch (retryError) {
                console.error('❌ Retry failed:', retryError);
            }
        }, 1000);
    }
}

// Initialize immediately if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSocialSharing);
} else {
    initializeSocialSharing();
}

// Also initialize on window load as a fallback for cPanel
window.addEventListener('load', () => {
    if (!window.socialSharingManager) {
        console.log('🔄 Fallback initialization on window load');
        initializeSocialSharing();
    }
});

// Add debugging functions for cPanel testing
window.testSocialSharing = function() {
    console.log('🧪 Testing social sharing functionality...');
    console.log('Environment info:', {
        hostname: window.location.hostname,
        pathname: window.location.pathname,
        origin: window.location.origin,
        basePath: window.socialSharingManager?.getBasePath()
    });

    if (!window.socialSharingManager) {
        console.error('❌ SocialSharingManager not initialized');
        return false;
    }

    const testGasData = {
        totalGasSpent: 1.234,
        totalTransactions: 150,
        address: '0x1234567890123456789012345678901234567890'
    };

    try {
        console.log('🚀 Testing sharing interface...');
        window.socialSharingManager.showSharingInterface(testGasData);
        console.log('✅ Sharing interface test successful');
        return true;
    } catch (error) {
        console.error('❌ Sharing interface test failed:', error);
        return false;
    }
};

// Add a function to test the allocation display
window.testAllocationDisplay = function() {
    console.log('🧪 Testing allocation display...');

    const testGasData = {
        totalGasSpent: 1.234,
        totalTransactions: 150,
        address: '0x1234567890123456789012345678901234567890'
    };

    if (window.airdropManager && window.airdropManager.displayAllocationInBox) {
        window.airdropManager.displayAllocationInBox(testGasData);
        console.log('✅ Allocation display test successful');
        return true;
    } else {
        console.error('❌ AirdropManager not available');
        return false;
    }
};

window.debugSocialSharing = function() {
    console.log('🔍 Social Sharing Debug Info:');
    console.log('- Manager exists:', !!window.socialSharingManager);
    console.log('- Modal exists:', !!document.getElementById('sharing-modal'));
    console.log('- Base path:', window.socialSharingManager?.getBasePath());
    console.log('- Image path:', window.socialSharingManager?.getImagePath('driplogo.PNG'));

    // Test image loading
    const testImg = new Image();
    testImg.onload = () => console.log('✅ Logo image loads successfully');
    testImg.onerror = () => console.log('❌ Logo image failed to load');
    testImg.src = window.socialSharingManager?.getImagePath('driplogo.PNG') || './images/driplogo.PNG';
};

// Add a test function for debugging
window.testSocialSharing = function() {
    console.log('🧪 Testing social sharing modal...');
    const testGasData = {
        totalGasSpent: 1.234,
        totalTransactions: 150,
        address: '0x1234567890123456789012345678901234567890'
    };

    if (window.socialSharingManager) {
        window.socialSharingManager.showSharingModal(testGasData);
    } else {
        console.error('❌ SocialSharingManager not available');
    }
};


<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" rx="8" fill="url(#rainbow-bg)"/>
  <circle cx="16" cy="16" r="12" fill="none" stroke="url(#rainbow-1)" stroke-width="2"/>
  <circle cx="16" cy="16" r="9" fill="none" stroke="url(#rainbow-2)" stroke-width="2"/>
  <circle cx="16" cy="16" r="6" fill="none" stroke="url(#rainbow-3)" stroke-width="2"/>
  <circle cx="16" cy="16" r="3" fill="url(#rainbow-center)"/>
  <defs>
    <linearGradient id="rainbow-bg" x1="0" y1="0" x2="32" y2="32" gradientUnits="userSpaceOnUse">
      <stop stop-color="#1E293B"/>
      <stop offset="1" stop-color="#0F172A"/>
    </linearGradient>
    <linearGradient id="rainbow-1" x1="4" y1="4" x2="28" y2="28" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FF0080"/>
      <stop offset="0.2" stop-color="#FF8C00"/>
      <stop offset="0.4" stop-color="#FFD700"/>
      <stop offset="0.6" stop-color="#00FF80"/>
      <stop offset="0.8" stop-color="#0080FF"/>
      <stop offset="1" stop-color="#8000FF"/>
    </linearGradient>
    <linearGradient id="rainbow-2" x1="7" y1="7" x2="25" y2="25" gradientUnits="userSpaceOnUse">
      <stop stop-color="#8000FF"/>
      <stop offset="0.2" stop-color="#0080FF"/>
      <stop offset="0.4" stop-color="#00FF80"/>
      <stop offset="0.6" stop-color="#FFD700"/>
      <stop offset="0.8" stop-color="#FF8C00"/>
      <stop offset="1" stop-color="#FF0080"/>
    </linearGradient>
    <linearGradient id="rainbow-3" x1="10" y1="10" x2="22" y2="22" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FF0080"/>
      <stop offset="0.33" stop-color="#FFD700"/>
      <stop offset="0.66" stop-color="#00FF80"/>
      <stop offset="1" stop-color="#0080FF"/>
    </linearGradient>
    <radialGradient id="rainbow-center" cx="16" cy="16" r="3" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFFFF"/>
      <stop offset="1" stop-color="#F0F9FF"/>
    </radialGradient>
  </defs>
</svg>

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-accent: #3b82f6;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Button Colors */
    --btn-primary: #3b82f6;
    --btn-primary-hover: #2563eb;
    --btn-secondary: #6b7280;
    --btn-secondary-hover: #4b5563;
    --btn-accent: #10b981;
    --btn-accent-hover: #059669;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
}

[data-theme="dark"] {
    /* Dark Theme */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-accent: #60a5fa;
    --border-color: #334155;
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

/* Links */
a {
    color: var(--text-accent);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--btn-primary-hover);
}

.link {
    color: var(--text-accent);
    text-decoration: underline;
}

/* Buttons */
.btn, .action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    gap: 0.5rem;
}

.action-btn.primary {
    background-color: var(--btn-primary);
    color: white;
}

.action-btn.primary:hover {
    background-color: var(--btn-primary-hover);
}

.action-btn.secondary {
    background-color: var(--btn-secondary);
    color: white;
}

.action-btn.secondary:hover {
    background-color: var(--btn-secondary-hover);
}

.action-btn.accent {
    background-color: var(--btn-accent);
    color: white;
}

.action-btn.accent:hover {
    background-color: var(--btn-accent-hover);
}

.action-btn.large {
    padding: 1rem 2rem;
    font-size: 1rem;
    width: 100%;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Cards */
.card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.page-header h1 {
    margin-bottom: 0.5rem;
}

.page-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.stat-content h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

/* Views */
.view {
    display: none;
    min-height: calc(100vh - 80px);
    padding-top: 80px;
}

.view.active {
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .page-header {
        padding: 1rem 0;
        margin-bottom: 2rem;
    }

    .page-header h1 {
        font-size: 2rem;
        line-height: 1.2;
    }

    .action-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
        min-height: 44px; /* Touch-friendly size */
    }

    /* Hero Section Mobile */
    .hero-section {
        padding: 6rem 0 4rem;
    }

    .hero-title {
        font-size: 2.5rem;
        line-height: 1.1;
    }

    .hero-subtitle {
        font-size: 1rem;
        line-height: 1.5;
    }

    .hero-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .cta-button {
        width: 100%;
        justify-content: center;
        min-height: 48px;
    }

    /* Cards and Components */
    .card {
        padding: 1.25rem;
    }

    .stat-card {
        padding: 1.25rem;
    }

    .stat-value {
        font-size: 1.75rem;
    }

    /* Form Elements */
    input, select, textarea {
        min-height: 44px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    button {
        min-height: 44px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }

    .page-header h1 {
        font-size: 1.75rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .action-btn.large {
        padding: 0.875rem 1.5rem;
    }

    /* Hero Section Small Mobile */
    .hero-section {
        padding: 5rem 0 3rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
    }

    .hero-preview {
        margin-top: 2rem;
    }

    .preview-gas-prices {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .preview-price-item {
        padding: 0.75rem;
    }

    .preview-eth-price {
        padding: 0.75rem;
    }

    /* Smaller cards on mobile */
    .card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }
}

@media (max-width: 360px) {
    .container {
        padding: 0 0.375rem;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .hero-subtitle {
        font-size: 0.85rem;
    }

    .card {
        padding: 0.875rem;
    }

    .stat-card {
        padding: 0.875rem;
    }

    .action-btn {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;
    }
}

/* Gas Prices Grid */
.gas-prices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.gas-price-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.gas-price-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--text-accent);
}

.gas-price-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.gas-price-card:hover::before {
    left: 100%;
}

.gas-card-content {
    position: relative;
    z-index: 1;
}

.gas-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.gas-header .gas-icon {
    font-size: 1.5rem;
}

.gas-header .gas-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.gas-price-card .gas-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.gas-breakdown {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.gas-cost {
    font-size: 0.875rem;
    color: var(--text-accent);
    font-weight: 500;
}

.eth-price-card {
    background: linear-gradient(135deg, var(--text-accent), var(--text-accent-hover));
    color: white;
    border: none;
}

.eth-price-card .gas-label,
.eth-price-card .gas-breakdown,
.eth-price-card .gas-cost {
    color: rgba(255, 255, 255, 0.9);
}

.eth-price-card .gas-price {
    color: white;
}

/* Gas Tracker Section */
.gas-tracker-section {
    margin-bottom: 3rem;
}

@media (max-width: 768px) {
    .gas-prices-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .gas-price-card {
        padding: 1rem;
        min-height: auto;
    }

    .gas-price-card .gas-price {
        font-size: 1.5rem;
    }

    .gas-price-card .gas-header {
        margin-bottom: 0.75rem;
    }

    .gas-price-card .gas-breakdown {
        font-size: 0.75rem;
    }

    .gas-price-card .gas-cost {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .gas-prices-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .gas-price-card {
        padding: 1rem;
    }

    .gas-header .gas-icon {
        font-size: 1.25rem;
    }

    .gas-price-card .gas-price {
        font-size: 1.5rem;
    }

    .gas-price-card .gas-header {
        margin-bottom: 0.5rem;
    }

    .gas-price-card .gas-label {
        font-size: 0.875rem;
    }

    .gas-price-card .gas-breakdown {
        font-size: 0.7rem;
        margin: 0.5rem 0;
    }

    .gas-price-card .gas-cost {
        font-size: 0.7rem;
    }
}

@media (max-width: 360px) {
    .gas-prices-grid {
        gap: 0.5rem;
    }

    .gas-price-card {
        padding: 0.875rem;
    }

    .gas-price-card .gas-price {
        font-size: 1.25rem;
    }

    .gas-header .gas-icon {
        font-size: 1rem;
    }

    .gas-price-card .gas-label {
        font-size: 0.8rem;
    }
}

/* Coming Soon Message Styles */
.coming-soon-message {
    text-align: center;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 2px dashed var(--border-color);
}

.coming-soon-message h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.coming-soon-message p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Airdrop Allocation Display Styles */
.allocation-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-radius: 16px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.allocation-icon {
    margin-bottom: 1rem;
}

.allocation-icon .token-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: var(--bg-primary);
    padding: 8px;
    box-shadow: var(--shadow-lg);
}

.allocation-text .allocation-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.allocation-text .allocation-amount {
    color: #e91e63;
    font-size: 3rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.allocation-text .allocation-token {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0.25rem;
    margin-bottom: 0;
}

/* Twitter Link in Navbar */
.twitter-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    color: #1da1f2;
    text-decoration: none;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    background: rgba(29, 161, 242, 0.1);
    border: 2px solid rgba(29, 161, 242, 0.3);
    box-shadow: 0 2px 8px rgba(29, 161, 242, 0.2);
}

.twitter-link:hover {
    color: #ffffff;
    background: #1da1f2;
    border-color: #1da1f2;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(29, 161, 242, 0.4);
}

.twitter-link svg {
    transition: transform 0.3s ease;
}

.twitter-link:hover svg {
    transform: scale(1.15);
}

/* Footer Styles */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    margin-top: 4rem;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
    margin-bottom: 2rem;
}

.footer-left {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-logo-img {
    width: 24px;
    height: 24px;
    border-radius: 0.375rem;
}

.footer-logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

.footer-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    max-width: 300px;
}

.footer-right {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-end;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
}

.social-link:hover {
    color: #1da1f2;
    background: rgba(29, 161, 242, 0.1);
    border-color: rgba(29, 161, 242, 0.3);
    transform: translateY(-2px);
}

.footer-links {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--text-primary);
}

.footer-bottom {
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.footer-bottom p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Responsive Footer */
@media (max-width: 768px) {
    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .footer-left {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .footer-logo {
        justify-content: center;
        margin-bottom: 1rem;
    }

    .footer-description {
        text-align: center;
        max-width: 300px;
    }

    .footer-right {
        align-items: center;
    }

    .footer-links {
        justify-content: center;
        gap: 1.5rem;
    }

    .footer-link {
        font-size: 0.875rem;
    }

    .footer-bottom {
        padding: 1rem 0;
        text-align: center;
    }

    .footer-bottom p {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .footer {
        padding: 1.5rem 0 0.75rem;
    }

    .footer-content {
        gap: 1rem;
    }

    .footer-left {
        align-items: center;
    }

    .footer-logo {
        justify-content: center;
        margin-bottom: 0.75rem;
    }

    .footer-logo-text {
        font-size: 1.125rem;
    }

    .footer-description {
        font-size: 0.875rem;
        max-width: 280px;
    }

    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }

    .footer-link {
        font-size: 0.8rem;
        padding: 0.5rem;
    }

    .footer-logo-img {
        width: 20px;
        height: 20px;
    }

    .footer-bottom {
        padding: 0.75rem 0;
    }

    .footer-bottom p {
        font-size: 0.75rem;
    }
}

/* Mobile-Specific Improvements */
@media (max-width: 768px) {
    /* Prevent horizontal scrolling */
    body {
        overflow-x: hidden;
    }

    /* Touch-friendly interactions */
    .cta-button,
    .action-btn,
    .connect-btn,
    .disconnect-btn {
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }

    /* Improve text readability */
    body {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
    }

    /* Better spacing for mobile */
    .section {
        padding: 2rem 0;
    }

    .section-header-center {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    .section-subtitle {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Mobile-optimized cards */
    .card,
    .gas-price-card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }

    /* Better mobile navigation */
    .nav-container {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Mobile-friendly modals and overlays */
    .modal,
    .overlay {
        padding: 1rem;
    }

    /* Ensure images are responsive */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Mobile-optimized tables */
    table {
        font-size: 0.875rem;
    }

    th, td {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    /* Smaller mobile optimizations */
    .section {
        padding: 1.5rem 0;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .section-subtitle {
        font-size: 0.85rem;
    }

    /* Compact mobile layout */
    .card,
    .gas-price-card {
        border-radius: 10px;
    }

    /* Mobile table improvements */
    table {
        font-size: 0.8rem;
    }

    th, td {
        padding: 0.375rem;
    }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        padding: 4rem 0 2rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .nav-container {
        height: 60px;
    }

    .gas-tracker {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
    }
}

class APIConfig {
    constructor() {
        this.endpoints = {
            etherscan: {
                baseUrl: 'https://api.etherscan.io/api',
                gasPrice: '/api?module=gastracker&action=gasoracle',
                apiKey: this.getApiKey('ETHERSCAN_API_KEY') || 'YourEtherscanAPIKey'
            },
            ethGasStation: {
                baseUrl: 'https://ethgasstation.info/api',
                gasPrice: '/ethgasAPI.json'
            },
            
            coingecko: {
                baseUrl: 'https://api.coingecko.com/api/v3',
                ethPrice: '/simple/price?ids=ethereum&vs_currencies=usd&include_24hr_change=true',
                tokenPrices: '/simple/price'
            },
            coinmarketcap: {
                baseUrl: 'https://pro-api.coinmarketcap.com/v1',
                apiKey: this.getApiKey('CMC_API_KEY') || 'YourCMCAPIKey'
            },
            
            dexscreener: {
                baseUrl: 'https://api.dexscreener.com/latest/dex',
                tokenPairs: '/tokens',
                search: '/search'
            },
            oneinch: {
                baseUrl: 'https://api.1inch.io/v5.0/1',
                quote: '/quote',
                swap: '/swap',
                tokens: '/tokens',
                apiKey: this.getApiKey('ONEINCH_API_KEY')
            },
            zeroex: {
                baseUrl: 'https://api.0x.org',
                quote: '/swap/v1/quote',
                price: '/swap/v1/price'
            },
            
            opensea: {
                baseUrl: 'https://api.opensea.io/api/v1',
                collection: '/collection',
                assets: '/assets',
                events: '/events',
                apiKey: this.getApiKey('OPENSEA_API_KEY')
            },
            alchemy: {
                baseUrl: 'https://eth-mainnet.g.alchemy.com/v2',
                apiKey: this.getApiKey('ALCHEMY_API_KEY') || 'YourAlchemyAPIKey'
            },
            
            infura: {
                baseUrl: 'https://mainnet.infura.io/v3',
                apiKey: this.getApiKey('INFURA_API_KEY') || 'YourInfuraAPIKey'
            },

            etherscan: {
                baseUrl: 'https://api.etherscan.io/api',
                apiKey: '**********************************'
            }
        };
        
        this.rateLimits = {
            etherscan: { requests: 5, window: 1000 }, 
            coingecko: { requests: 10, window: 60000 }, 
            opensea: { requests: 4, window: 1000 }, 
            oneinch: { requests: 10, window: 1000 }, 
            default: { requests: 5, window: 1000 }
        };
        
        this.cacheConfig = {
            gasPrice: { ttl: 30000 },
            ethPrice: { ttl: 60000 }, 
            tokenPrices: { ttl: 30000 }, 
            nftCollection: { ttl: 300000 }, 
            nftAssets: { ttl: 120000 }, 
            swapQuote: { ttl: 10000 }, 
            stakingData: { ttl: 60000 }
        };
        
        this.initializeCache();
        this.initializeRateLimiter();
    }
    
    getApiKey(keyName) {
        if (typeof process !== 'undefined' && process.env) {
            return process.env[keyName];
        }
        
        if (typeof localStorage !== 'undefined') {
            return localStorage.getItem(keyName);
        }
        
        return null;
    }
    
    initializeCache() {
        this.cache = new Map();
        this.cacheTimestamps = new Map();
    }
    
    initializeRateLimiter() {
        this.rateLimiters = new Map();
        
        Object.keys(this.rateLimits).forEach(api => {
            this.rateLimiters.set(api, {
                requests: [],
                config: this.rateLimits[api]
            });
        });
    }
    
    async checkRateLimit(apiName) {
        const limiter = this.rateLimiters.get(apiName) || this.rateLimiters.get('default');
        const now = Date.now();
        const config = limiter.config;
        
        limiter.requests = limiter.requests.filter(time => now - time < config.window);
        
        if (limiter.requests.length >= config.requests) {
            const oldestRequest = Math.min(...limiter.requests);
            const waitTime = config.window - (now - oldestRequest);
            
            if (waitTime > 0) {
                console.warn(`Rate limit reached for ${apiName}. Waiting ${waitTime}ms`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
                return this.checkRateLimit(apiName);
            }
        }
        
        limiter.requests.push(now);
        return true;
    }
    
    getCachedData(key) {
        const cached = this.cache.get(key);
        const timestamp = this.cacheTimestamps.get(key);
        
        if (!cached || !timestamp) return null;
        
        const cacheType = this.getCacheType(key);
        const config = this.cacheConfig[cacheType] || { ttl: 60000 };
        
        if (Date.now() - timestamp > config.ttl) {
            this.cache.delete(key);
            this.cacheTimestamps.delete(key);
            return null;
        }
        
        return cached;
    }
    
    setCachedData(key, data) {
        this.cache.set(key, data);
        this.cacheTimestamps.set(key, Date.now());
    }
    
    getCacheType(key) {
        if (key.includes('gas')) return 'gasPrice';
        if (key.includes('eth-price')) return 'ethPrice';
        if (key.includes('token-price')) return 'tokenPrices';
        if (key.includes('nft-collection')) return 'nftCollection';
        if (key.includes('nft-assets')) return 'nftAssets';
        if (key.includes('swap-quote')) return 'swapQuote';
        if (key.includes('staking')) return 'stakingData';
        return 'default';
    }
    
    async makeRequest(apiName, endpoint, options = {}) {
        const cacheKey = `${apiName}-${endpoint}-${JSON.stringify(options.params || {})}`;
        
        const cached = this.getCachedData(cacheKey);
        if (cached && !options.skipCache) {
            return cached;
        }
        
        await this.checkRateLimit(apiName);
        
        try {
            const apiConfig = this.endpoints[apiName];
            if (!apiConfig) {
                throw new Error(`Unknown API: ${apiName}`);
            }
            
            const url = new URL(endpoint, apiConfig.baseUrl);
            if (apiConfig.apiKey) {
                if (apiName === 'etherscan') {
                    url.searchParams.append('apikey', apiConfig.apiKey);
                } else if (apiName === 'coinmarketcap') {
                    options.headers = {
                        ...options.headers,
                        'X-CMC_PRO_API_KEY': apiConfig.apiKey
                    };
                } else if (apiName === 'opensea') {
                    options.headers = {
                        ...options.headers,
                        'X-API-KEY': apiConfig.apiKey
                    };
                }
            }
            
            if (options.params) {
                Object.entries(options.params).forEach(([key, value]) => {
                    url.searchParams.append(key, value);
                });
            }
            
            const response = await fetch(url.toString(), {
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: options.body ? JSON.stringify(options.body) : undefined
            });
            
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (!options.skipCache) {
                this.setCachedData(cacheKey, data);
            }
            
            return data;
            
        } catch (error) {
            console.error(`API request failed for ${apiName}:`, error);
            throw error;
        }
    }
    
    getBestAPI(service) {
        const apiPriority = {
            gasPrice: ['etherscan', 'ethGasStation'],
            ethPrice: ['coingecko', 'coinmarketcap'],
            tokenPrices: ['coingecko', 'dexscreener'],
            swapQuote: ['oneinch', 'zeroex'],
            nftData: ['opensea', 'alchemy']
        };
        
        return apiPriority[service] || [];
    }
    
    async checkAPIHealth() {
        const results = {};
        
        for (const [apiName, config] of Object.entries(this.endpoints)) {
            try {
                const startTime = Date.now();
                
                let testEndpoint = '/';
                if (apiName === 'coingecko') testEndpoint = '/ping';
                if (apiName === 'etherscan') testEndpoint = '/api?module=stats&action=ethsupply';
                
                await this.makeRequest(apiName, testEndpoint, { skipCache: true });
                
                results[apiName] = {
                    status: 'healthy',
                    responseTime: Date.now() - startTime
                };
            } catch (error) {
                results[apiName] = {
                    status: 'unhealthy',
                    error: error.message
                };
            }
        }
        
        return results;
    }
    
    clearCache(pattern = null) {
        if (pattern) {
            for (const key of this.cache.keys()) {
                if (key.includes(pattern)) {
                    this.cache.delete(key);
                    this.cacheTimestamps.delete(key);
                }
            }
        } else {
            this.cache.clear();
            this.cacheTimestamps.clear();
        }
    }

    async getEtherscanData(module, action, additionalParams = {}) {
        const params = {
            module: module,
            action: action,
            apikey: this.endpoints.etherscan.apiKey,
            ...additionalParams
        };

        const url = new URL(this.endpoints.etherscan.baseUrl);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                url.searchParams.append(key, params[key]);
            }
        });

        try {
            const response = await fetch(url.toString());
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            if (data.status === '1') {
                return data.result;
            } else {
                throw new Error(data.message || 'Etherscan API error');
            }
        } catch (error) {
            console.error('Etherscan API error:', error);
            throw error;
        }
    }

    async getGasPrices() {
        return await this.getEtherscanData('gastracker', 'gasoracle');
    }

    async getAccountTransactions(address, startblock = 0, endblock = ********, page = 1, offset = 10) {
        try {
            return await this.getEtherscanData('account', 'txlist', {
                address: address,
                startblock: startblock,
                endblock: endblock,
                page: page,
                offset: offset,
                sort: 'desc'
            });
        } catch (error) {
            return [];
        }
    }

    async getAccountBalance(address) {
        try {
            const balance = await this.getEtherscanData('account', 'balance', {
                address: address,
                tag: 'latest'
            });
            return (parseInt(balance) / Math.pow(10, 18)).toFixed(6);
        } catch (error) {
            console.error('Error fetching account balance:', error);
            return '0.000000';
        }
    }

    async getTransactionReceipt(txhash) {
        try {
            return await this.getEtherscanData('proxy', 'eth_getTransactionReceipt', {
                txhash: txhash
            });
        } catch (error) {
            console.error('Error fetching transaction receipt:', error);
            return null;
        }
    }
}

window.apiConfig = new APIConfig();

if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIConfig;
}

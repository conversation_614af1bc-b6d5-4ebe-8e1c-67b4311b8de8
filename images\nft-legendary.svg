<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" rx="12" fill="url(#legendary-bg)"/>
  <!-- Crown base -->
  <path d="M12 40L52 40L48 52L16 52L12 40Z" fill="url(#crown-base)"/>
  <!-- Crown peaks -->
  <path d="M16 24L20 40L12 40L16 24Z" fill="url(#crown-peak)"/>
  <path d="M32 12L36 40L28 40L32 12Z" fill="url(#crown-peak)"/>
  <path d="M48 24L52 40L44 40L48 24Z" fill="url(#crown-peak)"/>
  <path d="M24 32L28 40L20 40L24 32Z" fill="url(#crown-peak)"/>
  <path d="M40 32L44 40L36 40L40 32Z" fill="url(#crown-peak)"/>
  <!-- Gems -->
  <circle cx="32" cy="26" r="3" fill="url(#gem-center)"/>
  <circle cx="20" cy="36" r="2" fill="url(#gem-side)"/>
  <circle cx="44" cy="36" r="2" fill="url(#gem-side)"/>
  <!-- Sparkles -->
  <path d="M8 20L10 22L8 24L6 22L8 20Z" fill="#FBBF24"/>
  <path d="M56 20L58 22L56 24L54 22L56 20Z" fill="#FBBF24"/>
  <path d="M12 8L14 10L12 12L10 10L12 8Z" fill="#FBBF24"/>
  <path d="M52 8L54 10L52 12L50 10L52 8Z" fill="#FBBF24"/>
  <defs>
    <linearGradient id="legendary-bg" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#581C87"/>
      <stop offset="1" stop-color="#3B0764"/>
    </linearGradient>
    <linearGradient id="crown-base" x1="12" y1="40" x2="52" y2="52" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FBBF24"/>
      <stop offset="1" stop-color="#D97706"/>
    </linearGradient>
    <linearGradient id="crown-peak" x1="0" y1="12" x2="64" y2="40" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FEF3C7"/>
      <stop offset="0.5" stop-color="#FBBF24"/>
      <stop offset="1" stop-color="#F59E0B"/>
    </linearGradient>
    <radialGradient id="gem-center" cx="32" cy="26" r="3" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFFFF"/>
      <stop offset="0.5" stop-color="#A78BFA"/>
      <stop offset="1" stop-color="#7C3AED"/>
    </radialGradient>
    <radialGradient id="gem-side" cx="50%" cy="50%" r="50%" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFFFF"/>
      <stop offset="0.5" stop-color="#34D399"/>
      <stop offset="1" stop-color="#10B981"/>
    </radialGradient>
  </defs>
</svg>

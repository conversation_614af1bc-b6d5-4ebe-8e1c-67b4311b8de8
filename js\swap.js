class SwapManager {
    constructor() {
        this.fromToken = {
            symbol: 'ETH',
            address: '******************************************',
            decimals: 18,
            balance: 0
        };
        
        this.toToken = {
            symbol: 'WTF',
            address: '******************************************', 
            decimals: 18,
            balance: 0
        };
        
        this.swapRate = 0;
        this.priceImpact = 0;
        this.minReceived = 0;
        this.networkFee = 0;
        
        this.init();
    }

    init() {
        console.log('🔄 Initializing Swap Manager...');
        
        this.initSwapUI();
        this.initTokenModals();
        this.loadTokenBalances();
        
        console.log('✅ Swap Manager initialized');
    }

    initSwapUI() {
        const fromAmountInput = document.getElementById('from-amount');
        const toAmountInput = document.getElementById('to-amount');
        
        if (fromAmountInput) {
            fromAmountInput.addEventListener('input', () => {
                this.calculateSwapAmount();
            });
        }

        const fromTokenBtn = document.getElementById('from-token');
        const toTokenBtn = document.getElementById('to-token');
        
        if (fromTokenBtn) {
            fromTokenBtn.addEventListener('click', () => {
                this.showTokenModal('from');
            });
        }
        
        if (toTokenBtn) {
            toTokenBtn.addEventListener('click', () => {
                this.showTokenModal('to');
            });
        }

        const swapDirectionBtn = document.getElementById('swap-direction');
        if (swapDirectionBtn) {
            swapDirectionBtn.addEventListener('click', () => {
                this.swapTokenDirection();
            });
        }

        const swapBtn = document.getElementById('swap-btn');
        if (swapBtn) {
            swapBtn.addEventListener('click', () => {
                this.executeSwap();
            });
        }

        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSwapSettings();
            });
        }

        this.updateSwapButton();
    }

    initTokenModals() {
        const tokenModal = document.getElementById('token-modal-overlay');
        const closeTokenModal = document.getElementById('close-token-modal');
        
        if (closeTokenModal) {
            closeTokenModal.addEventListener('click', () => {
                this.hideTokenModal();
            });
        }
        
        if (tokenModal) {
            tokenModal.addEventListener('click', (e) => {
                if (e.target === tokenModal) {
                    this.hideTokenModal();
                }
            });
        }

        const tokenSearch = document.getElementById('token-search');
        if (tokenSearch) {
            tokenSearch.addEventListener('input', (e) => {
                this.filterTokens(e.target.value);
            });
        }

        this.loadTokenList();
    }

    showTokenModal(type) {
        this.currentTokenSelection = type;
        const modal = document.getElementById('token-modal-overlay');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    hideTokenModal() {
        const modal = document.getElementById('token-modal-overlay');
        if (modal) {
            modal.classList.add('hidden');
        }
        this.currentTokenSelection = null;
    }

    loadTokenList() {
        const tokenList = document.getElementById('token-list');
        if (!tokenList) return;

        const tokens = [
            { symbol: 'ETH', name: 'Ethereum', address: '******************************************', icon: './images/ethereum-logo.png' },
            { symbol: 'WTF', name: 'WTF Token', address: '******************************************', icon: './images/wtf-logo.png' },
            { symbol: 'USDC', name: 'USD Coin', address: '******************************************', icon: './images/usdc-logo.png' },
            { symbol: 'USDT', name: 'Tether USD', address: '******************************************', icon: './images/usdt-logo.png' }
        ];

        tokenList.innerHTML = '';
        
        tokens.forEach(token => {
            const tokenItem = document.createElement('div');
            tokenItem.className = 'token-item';
            tokenItem.innerHTML = `
                <div class="token-info">
                    <img src="${token.icon}" alt="${token.symbol}" class="token-icon" onerror="this.style.display='none'">
                    <div class="token-details">
                        <div class="token-symbol">${token.symbol}</div>
                        <div class="token-name">${token.name}</div>
                    </div>
                </div>
                <div class="token-balance">0</div>
            `;
            
            tokenItem.addEventListener('click', () => {
                this.selectToken(token);
            });
            
            tokenList.appendChild(tokenItem);
        });
    }

    selectToken(token) {
        if (this.currentTokenSelection === 'from') {
            this.fromToken = { ...token, balance: 0 };
            this.updateFromTokenUI();
        } else if (this.currentTokenSelection === 'to') {
            this.toToken = { ...token, balance: 0 };
            this.updateToTokenUI();
        }
        
        this.hideTokenModal();
        this.calculateSwapAmount();
        this.loadTokenBalances();
    }

    updateFromTokenUI() {
        const fromTokenBtn = document.getElementById('from-token');
        if (fromTokenBtn) {
            const tokenIcon = fromTokenBtn.querySelector('.token-icon');
            const tokenSymbol = fromTokenBtn.querySelector('span');
            
            if (tokenIcon) tokenIcon.src = this.fromToken.icon || './images/ethereum-logo.png';
            if (tokenSymbol) tokenSymbol.textContent = this.fromToken.symbol;
        }

        const fromBalance = document.getElementById('from-balance');
        if (fromBalance && window.utils) {
            fromBalance.textContent = window.utils.formatNumber(this.fromToken.balance, 4);
        }
    }

    updateToTokenUI() {
        const toTokenBtn = document.getElementById('to-token');
        if (toTokenBtn) {
            const tokenIcon = toTokenBtn.querySelector('.token-icon');
            const tokenSymbol = toTokenBtn.querySelector('span');
            
            if (tokenIcon) tokenIcon.src = this.toToken.icon || './images/wtf-logo.png';
            if (tokenSymbol) tokenSymbol.textContent = this.toToken.symbol;
        }

        const toBalance = document.getElementById('to-balance');
        if (toBalance && window.utils) {
            toBalance.textContent = window.utils.formatNumber(this.toToken.balance, 4);
        }
    }

    swapTokenDirection() {
        const temp = { ...this.fromToken };
        this.fromToken = { ...this.toToken };
        this.toToken = temp;
        
        this.updateFromTokenUI();
        this.updateToTokenUI();
        
        const fromAmount = document.getElementById('from-amount');
        const toAmount = document.getElementById('to-amount');
        
        if (fromAmount && toAmount) {
            const tempValue = fromAmount.value;
            fromAmount.value = toAmount.value;
            toAmount.value = tempValue;
        }
        
        this.calculateSwapAmount();
    }

    async calculateSwapAmount() {
        const fromAmountInput = document.getElementById('from-amount');
        const toAmountInput = document.getElementById('to-amount');
        
        if (!fromAmountInput || !toAmountInput) return;
        
        const fromAmount = parseFloat(fromAmountInput.value);
        
        if (!fromAmount || fromAmount <= 0) {
            toAmountInput.value = '';
            this.updateSwapInfo(0, 0, 0, 0);
            return;
        }

        try {
            const quote = await this.getSwapQuote(fromAmount);
            
            toAmountInput.value = quote.toAmount.toFixed(6);
            this.updateSwapInfo(quote.rate, quote.priceImpact, quote.minReceived, quote.networkFee);
            
        } catch (error) {
            console.error('Error calculating swap amount:', error);
            toAmountInput.value = '';
        }
    }

    async getSwapQuote(fromAmount) {
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const mockRate = this.fromToken.symbol === 'ETH' ? 1000 : 0.001;
        const toAmount = fromAmount * mockRate;
        
        return {
            toAmount: toAmount,
            rate: mockRate,
            priceImpact: Math.random() * 0.5, 
            minReceived: toAmount * 0.995, 
            networkFee: 0.002 * 2000 
        };
    }

    updateSwapInfo(rate, priceImpact, minReceived, networkFee) {
        const swapRate = document.getElementById('swap-rate');
        const priceImpactEl = document.getElementById('price-impact');
        const minReceivedEl = document.getElementById('min-received');
        const networkFeeEl = document.getElementById('network-fee');

        if (swapRate) {
            swapRate.textContent = `1 ${this.fromToken.symbol} = ${rate.toFixed(2)} ${this.toToken.symbol}`;
        }

        if (priceImpactEl && window.utils) {
            priceImpactEl.textContent = window.utils.formatPercentage(priceImpact);
            priceImpactEl.className = 'impact-' + (priceImpact < 1 ? 'low' : priceImpact < 3 ? 'medium' : 'high');
        }

        if (minReceivedEl && window.utils) {
            minReceivedEl.textContent = `${window.utils.formatNumber(minReceived, 4)} ${this.toToken.symbol}`;
        }

        if (networkFeeEl && window.utils) {
            networkFeeEl.textContent = window.utils.formatCurrency(networkFee);
        }

        this.swapRate = rate;
        this.priceImpact = priceImpact;
        this.minReceived = minReceived;
        this.networkFee = networkFee;
    }

    updateSwapButton() {
        const swapBtn = document.getElementById('swap-btn');
        if (!swapBtn) return;

        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            swapBtn.textContent = 'Connect Wallet';
            swapBtn.disabled = false;
        } else {
            swapBtn.textContent = 'Swap';
            swapBtn.disabled = false;
        }
    }

    async executeSwap() {
        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            if (window.feesWTFApp) {
                window.feesWTFApp.showConnectModal();
            }
            return;
        }

        const fromAmountInput = document.getElementById('from-amount');
        const fromAmount = parseFloat(fromAmountInput.value);

        if (!fromAmount || fromAmount <= 0) {
            if (window.feesWTFApp) {
                window.feesWTFApp.showError('Please enter a valid amount');
            }
            return;
        }

        if (fromAmount > this.fromToken.balance) {
            if (window.feesWTFApp) {
                window.feesWTFApp.showError('Insufficient balance');
            }
            return;
        }

        try {
            console.log(`Swapping ${fromAmount} ${this.fromToken.symbol} for ${this.toToken.symbol}...`);
            
            await this.simulateTransaction();
            
            if (window.feesWTFApp) {
                window.feesWTFApp.showSuccess(`Successfully swapped ${fromAmount} ${this.fromToken.symbol}!`);
            }
            
            fromAmountInput.value = '';
            document.getElementById('to-amount').value = '';
            
            this.loadTokenBalances();
            
        } catch (error) {
            console.error('Error executing swap:', error);
            if (window.feesWTFApp) {
                window.feesWTFApp.showError('Swap failed. Please try again.');
            }
        }
    }

    async loadTokenBalances() {
        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            return;
        }

        try {
            if (window.walletManager) {
                const ethBalance = await window.walletManager.getBalance();
                if (this.fromToken.symbol === 'ETH') {
                    this.fromToken.balance = ethBalance;
                }
                if (this.toToken.symbol === 'ETH') {
                    this.toToken.balance = ethBalance;
                }
            }

            this.updateFromTokenUI();
            this.updateToTokenUI();
            
        } catch (error) {
            console.error('Error loading token balances:', error);
        }
    }

    filterTokens(searchTerm) {
        const tokenItems = document.querySelectorAll('.token-item');
        const term = searchTerm.toLowerCase();
        
        tokenItems.forEach(item => {
            const symbol = item.querySelector('.token-symbol').textContent.toLowerCase();
            const name = item.querySelector('.token-name').textContent.toLowerCase();
            
            if (symbol.includes(term) || name.includes(term)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    showSwapSettings() {
        console.log('Swap settings modal coming soon...');
        if (window.feesWTFApp) {
            window.feesWTFApp.showError('Swap settings coming soon');
        }
    }

    async simulateTransaction() {
        return new Promise(resolve => setTimeout(resolve, 3000));
    }
}

window.swapManager = new SwapManager();

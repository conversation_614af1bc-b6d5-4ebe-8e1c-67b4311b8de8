<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" rx="12" fill="url(#epic-bg)"/>
  <!-- Shield shape -->
  <path d="M32 8L48 16V32C48 44 32 56 32 56C32 56 16 44 16 32V16L32 8Z" fill="url(#epic-shield)"/>
  <!-- Inner shield -->
  <path d="M32 16L40 20V32C40 40 32 48 32 48C32 48 24 40 24 32V20L32 16Z" fill="url(#epic-inner)"/>
  <!-- Center emblem -->
  <circle cx="32" cy="28" r="6" fill="url(#epic-emblem)"/>
  <!-- Lightning bolt -->
  <path d="M30 24L34 28L32 32L36 28L32 24L30 28L32 32L28 28L30 24Z" fill="white"/>
  <!-- Energy rings -->
  <circle cx="32" cy="32" r="12" fill="none" stroke="url(#epic-ring)" stroke-width="1" opacity="0.6"/>
  <circle cx="32" cy="32" r="16" fill="none" stroke="url(#epic-ring)" stroke-width="1" opacity="0.4"/>
  <defs>
    <linearGradient id="epic-bg" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#1E40AF"/>
      <stop offset="1" stop-color="#1E3A8A"/>
    </linearGradient>
    <linearGradient id="epic-shield" x1="16" y1="8" x2="48" y2="56" gradientUnits="userSpaceOnUse">
      <stop stop-color="#8B5CF6"/>
      <stop offset="0.5" stop-color="#7C3AED"/>
      <stop offset="1" stop-color="#6D28D9"/>
    </linearGradient>
    <linearGradient id="epic-inner" x1="24" y1="16" x2="40" y2="48" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C4B5FD"/>
      <stop offset="1" stop-color="#8B5CF6"/>
    </linearGradient>
    <radialGradient id="epic-emblem" cx="32" cy="28" r="6" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFFFF"/>
      <stop offset="0.5" stop-color="#DDD6FE"/>
      <stop offset="1" stop-color="#8B5CF6"/>
    </radialGradient>
    <linearGradient id="epic-ring" x1="20" y1="20" x2="44" y2="44" gradientUnits="userSpaceOnUse">
      <stop stop-color="#60A5FA"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>
  </defs>
</svg>

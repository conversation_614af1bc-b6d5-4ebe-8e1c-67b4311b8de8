/* Navigation Styles */
#navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

/* Logo */
.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.logo-img {
    width: 32px;
    height: 32px;
}

.logo-text {
    background: linear-gradient(135deg, var(--btn-primary), var(--btn-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation Links */
.nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

.nav-link.active {
    color: var(--text-accent);
    background-color: var(--bg-tertiary);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: var(--text-accent);
    border-radius: 1px;
}

/* Right Side Navigation */
.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Gas Tracker */
.gas-tracker {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.gas-icon {
    font-size: 1rem;
}

.gas-price {
    font-weight: 600;
    color: var(--text-primary);
}

.gas-unit {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

/* Theme Toggle */
.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background-color: var(--bg-tertiary);
}

.theme-icon {
    font-size: 1.125rem;
    transition: transform 0.2s ease;
}

.theme-toggle:hover .theme-icon {
    transform: rotate(180deg);
}

/* Connect Wallet Button */
.connect-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--btn-primary), var(--btn-accent));
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.connect-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.wallet-icon {
    font-size: 1rem;
}

/* Wallet Info */
.wallet-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    min-width: 200px;
    max-width: 250px;
    overflow: hidden;
}

.wallet-address {
    font-size: 0.75rem;
    font-family: 'Monaco', 'Menlo', monospace;
    color: var(--text-secondary);
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.wallet-balance {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.disconnect-btn {
    padding: 0.5rem 1rem;
    background-color: var(--error);
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.disconnect-btn:hover {
    background-color: #dc2626;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 0.75rem;
        height: 70px;
    }

    .nav-left {
        gap: 0.75rem;
    }

    .nav-links {
        display: none;
    }

    .nav-right {
        gap: 0.375rem;
    }

    .gas-tracker {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
        min-width: auto;
    }

    .gas-tracker .gas-icon {
        font-size: 0.875rem;
    }

    .gas-tracker .gas-unit {
        font-size: 0.65rem;
    }

    .connect-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        gap: 0.25rem;
    }

    .wallet-text {
        display: none;
    }

    .theme-toggle {
        width: 32px;
        height: 32px;
    }

    .theme-icon {
        font-size: 1rem;
    }

    .twitter-link {
        padding: 0.5rem;
        width: 32px;
        height: 32px;
    }

    .twitter-link svg {
        width: 18px;
        height: 18px;
    }

    .wallet-info {
        min-width: 160px;
        padding: 0.75rem;
    }

    .wallet-address {
        font-size: 0.65rem;
        white-space: normal;
        word-break: break-all;
        line-height: 1.2;
    }

    .wallet-balance {
        font-size: 0.75rem;
    }

    .disconnect-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.65rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        height: 60px;
        padding: 0 0.5rem;
    }

    .logo-text {
        display: none;
    }

    .logo-img {
        width: 28px;
        height: 28px;
    }

    .nav-right {
        gap: 0.25rem;
    }

    .gas-tracker {
        padding: 0.25rem 0.375rem;
        font-size: 0.65rem;
    }

    .gas-tracker .gas-price {
        font-size: 0.65rem;
    }

    .theme-toggle {
        width: 28px;
        height: 28px;
    }

    .theme-icon {
        font-size: 0.875rem;
    }

    .twitter-link {
        width: 28px;
        height: 28px;
        padding: 0.375rem;
    }

    .twitter-link svg {
        width: 16px;
        height: 16px;
    }

    .connect-btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.7rem;
    }

    .wallet-info {
        min-width: 140px;
        padding: 0.5rem;
    }

    .wallet-address {
        font-size: 0.6rem;
        white-space: normal;
        word-break: break-all;
        line-height: 1.2;
    }

    .wallet-balance {
        font-size: 0.7rem;
    }
}

@media (max-width: 360px) {
    .nav-container {
        padding: 0 0.375rem;
    }

    .nav-left {
        gap: 0.5rem;
    }

    .nav-right {
        gap: 0.125rem;
    }

    .gas-tracker {
        display: none;
    }

    .twitter-link {
        display: none;
    }

    .wallet-info {
        min-width: 120px;
        padding: 0.375rem;
    }

    .wallet-address {
        font-size: 0.55rem;
        white-space: normal;
        word-break: break-all;
        line-height: 1.1;
    }

    .wallet-balance {
        font-size: 0.65rem;
    }

    .theme-toggle {
        width: 24px;
        height: 24px;
    }

    .theme-icon {
        font-size: 0.75rem;
    }
}

/* Mobile Menu (for future implementation) */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    gap: 3px;
    width: 24px;
    height: 24px;
    cursor: pointer;
}

.mobile-menu-btn span {
    width: 100%;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.2s ease;
}

@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex;
    }
}

/* Pro Dashboard Indicator */
.pro-indicator {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 8px;
    height: 8px;
    background-color: var(--btn-accent);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

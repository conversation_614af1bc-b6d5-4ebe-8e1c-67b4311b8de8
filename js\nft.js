class NFTManager {
    constructor() {
        this.collectionData = {
            totalSupply: 8610,
            floorPrice: 0.013,
            volume: 1234,
            holders: 3456
        };

        this.userNFTs = [];
        this.featuredNFTs = [];
        this.openseaApiUrl = 'https://gql.opensea.io/graphql';
        this.fallbackData = this.getFallbackNFTData();

        this.init();
    }

    init() {
        console.log('🖼️ Initializing NFT Manager...');
        
        this.initNFTUI();
        this.loadCollectionData();
        this.loadFeaturedNFTs();
        
        console.log('✅ NFT Manager initialized');
    }

    initNFTUI() {
        const mintBtn = document.getElementById('mint-nft');
        if (mintBtn) {
            mintBtn.addEventListener('click', () => {
                this.mintNFT();
            });
        }

        
        this.updateCollectionStats();
    }

    async loadCollectionData() {
        try {
            const collectionData = await this.fetchOpenSeaCollection();
            if (collectionData) {
                this.collectionData = collectionData;
            } else {
                this.collectionData = {
                    totalSupply: 8610,
                    floorPrice: 0.013,
                    volume: 1234.5,
                    holders: 3456
                };
            }

            const contractInfo = await this.getContractInfo();
            if (contractInfo) {
                console.log('Contract activity:', contractInfo);
                if (window.feesWTFApp) {
                    window.feesWTFApp.showToast(`Contract has ${contractInfo.recentTransactions} recent transactions`, 'info', 4000);
                }
            }

            this.updateCollectionStats();

        } catch (error) {
            console.error('Error loading collection data:', error);
            this.collectionData = {
                totalSupply: 8610,
                floorPrice: 0.013,
                volume: 1234.5,
                holders: 3456
            };
            this.updateCollectionStats();
        }
    }

    async fetchOpenSeaCollection() {
        try {
            const query = `
                query CollectionStatsQuery($slug: String!) {
                    collection(slug: $slug) {
                        id
                        name
                        description
                        imageUrl
                        floorPrice {
                            pricePerItem {
                                usd
                                token {
                                    unit
                                    symbol
                                    __typename
                                }
                                __typename
                            }
                            __typename
                        }
                        totalSupply
                        __typename
                    }
                }
            `;

            const variables = {
                slug: "fees-wtf-nft"
            };

            const response = await fetch(this.openseaApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operationName: "CollectionStatsQuery",
                    query: query,
                    variables: variables
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.data && data.data.collection) {
                const collection = data.data.collection;
                return {
                    totalSupply: collection.totalSupply || 8610,
                    floorPrice: collection.floorPrice?.pricePerItem?.token?.unit || 0.013,
                    volume: 1234.5, 
                    holders: 3456 
                };
            }

            return null;
        } catch (error) {
            console.warn('OpenSea API not available, using fallback data:', error);
            return null;
        }
    }

    updateCollectionStats() {
        const statsCards = document.querySelectorAll('#nft-view .stat-card');
        
        if (statsCards.length >= 4) {
            const totalSupplyValue = statsCards[0].querySelector('.stat-value');
            if (totalSupplyValue && window.utils) {
                totalSupplyValue.textContent = window.utils.formatNumber(this.collectionData.totalSupply);
            }

            const floorPriceValue = statsCards[1].querySelector('.stat-value');
            if (floorPriceValue) {
                floorPriceValue.textContent = `${this.collectionData.floorPrice} ETH`;
            }

            const volumeValue = statsCards[2].querySelector('.stat-value');
            if (volumeValue && window.utils) {
                volumeValue.textContent = `${window.utils.formatNumber(this.collectionData.volume)} ETH`;
            }

            const holdersValue = statsCards[3].querySelector('.stat-value');
            if (holdersValue && window.utils) {
                holdersValue.textContent = window.utils.formatNumber(this.collectionData.holders);
            }
        }
    }

    async loadFeaturedNFTs() {
        try {
            const realNFTs = await this.fetchFeaturedNFTs();
            if (realNFTs && realNFTs.length > 0) {
                this.featuredNFTs = realNFTs;
            } else {
                this.featuredNFTs = this.generateMockNFTs(12);
            }
            this.displayNFTs();

        } catch (error) {
            console.error('Error loading featured NFTs:', error);
            this.featuredNFTs = this.generateMockNFTs(12);
            this.displayNFTs();
        }
    }

    async fetchFeaturedNFTs() {
        try {
            const sampleTokenIds = ["2581", "1234", "5678", "9012", "3456", "7890"];
            const nfts = [];

            for (const tokenId of sampleTokenIds.slice(0, 6)) {
                const nftData = await this.fetchNFTById(tokenId);
                if (nftData) {
                    nfts.push(nftData);
                }
            }

            return nfts.length > 0 ? nfts : null;
        } catch (error) {
            console.warn('Error fetching featured NFTs:', error);
            return null;
        }
    }

    async fetchNFTById(tokenId) {
        try {
            const query = `
                query ItemViewModalQuery($identifier: ItemIdentifierInput!) {
                    itemByIdentifier(identifier: $identifier) {
                        __typename
                        ... on Item {
                            id
                            name
                            tokenId
                            imageUrl
                            description
                            attributes {
                                traitType
                                value
                                __typename
                            }
                            collection {
                                name
                                slug
                                floorPrice {
                                    pricePerItem {
                                        usd
                                        token {
                                            unit
                                            symbol
                                            __typename
                                        }
                                        __typename
                                    }
                                    __typename
                                }
                                __typename
                            }
                            bestListing {
                                pricePerItem {
                                    usd
                                    token {
                                        unit
                                        symbol
                                        __typename
                                    }
                                    __typename
                                }
                                __typename
                            }
                            rarity {
                                rank
                                totalSupply
                                __typename
                            }
                            __typename
                        }
                    }
                }
            `;

            const variables = {
                identifier: {
                    chain: "ethereum",
                    contractAddress: "******************************************",
                    tokenId: tokenId
                }
            };

            const response = await fetch(this.openseaApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operationName: "ItemViewModalQuery",
                    query: query,
                    variables: variables
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.data && data.data.itemByIdentifier) {
                return this.transformOpenSeaNFT(data.data.itemByIdentifier);
            }

            return null;
        } catch (error) {
            console.warn(`Error fetching NFT ${tokenId}:`, error);
            return null;
        }
    }

    transformOpenSeaNFT(item) {
        const attributes = item.attributes || [];
        const totalFees = attributes.find(attr => attr.traitType === 'Total Fees')?.value || 'N/A';
        const spenderLevel = attributes.find(attr => attr.traitType === 'Spender Level')?.value || 'N/A';
        const oofLevel = attributes.find(attr => attr.traitType === 'Oof Level')?.value || 'N/A';

        return {
            id: item.tokenId,
            name: item.name || `Fees.WTF #${item.tokenId}`,
            image: item.imageUrl || this.getFallbackImage(item.tokenId),
            description: item.description || '',
            attributes: {
                totalFees: totalFees,
                spenderLevel: spenderLevel,
                oofLevel: oofLevel,
                totalTransactions: attributes.find(attr => attr.traitType === 'Total Transactions')?.value || 'N/A',
                averageGwei: attributes.find(attr => attr.traitType === 'Average Gwei')?.value || 'N/A'
            },
            price: item.bestListing?.pricePerItem?.token?.unit || item.collection?.floorPrice?.pricePerItem?.token?.unit || '0.013',
            priceUSD: item.bestListing?.pricePerItem?.usd || item.collection?.floorPrice?.pricePerItem?.usd || 48.94,
            rarity: item.rarity?.rank || Math.floor(Math.random() * 8610) + 1,
            totalSupply: item.rarity?.totalSupply || 8610,
            tokenId: item.tokenId,
            contractAddress: "******************************************"
        };
    }

    getFallbackImage(tokenId) {
        return `https://raw2.seadn.io/ethereum/******************************************/placeholder_${tokenId}.svg`;
    }

    getFallbackNFTData() {
        return {
            id: "2581",
            name: "0xd15ace…f112fc",
            image: "https://raw2.seadn.io/ethereum/******************************************/a6fe4d9d48c9fd88ea9393749c9fafa0.svg",
            description: "fees.wtf snapshot at block 13916450",
            attributes: {
                totalFees: "4.24888",
                failFees: "0.12972",
                totalGas: "76595849",
                averageGwei: "57.99632",
                totalTransactions: "663",
                failedTransactions: "35",
                spenderLevel: "18",
                oofLevel: "13"
            },
            price: "0.014",
            priceUSD: 52.70,
            rarity: 4893,
            totalSupply: 8610,
            tokenId: "2581",
            contractAddress: "******************************************"
        };
    }

    async getContractInfo() {
        if (!window.apiConfig) {
            return null;
        }

        try {
            const contractAddress = "******************************************";
            const transactions = await window.apiConfig.getAccountTransactions(contractAddress, 0, ********, 1, 10);

            return {
                contractAddress: contractAddress,
                recentTransactions: transactions.length,
                lastActivity: transactions.length > 0 ? new Date(parseInt(transactions[0].timeStamp) * 1000) : null
            };
        } catch (error) {
            console.error('Error fetching contract info:', error);
            return null;
        }
    }

    generateMockNFTs(count) {
        const nfts = [];
        const traits = ['Rare', 'Epic', 'Legendary', 'Common', 'Uncommon'];
        const backgrounds = ['Blue', 'Red', 'Green', 'Purple', 'Gold', 'Silver'];

        nfts.push(this.fallbackData);

        for (let i = 2; i <= count; i++) {
            nfts.push({
                id: i.toString(),
                name: `Fees.WTF #${i.toString().padStart(4, '0')}`,
                image: `https://via.placeholder.com/300x300/4a90e2/ffffff?text=WTF%20%23${i}`,
                attributes: {
                    totalFees: (Math.random() * 10).toFixed(5),
                    spenderLevel: Math.floor(Math.random() * 20) + 1,
                    oofLevel: Math.floor(Math.random() * 15) + 1,
                    totalTransactions: Math.floor(Math.random() * 1000) + 100,
                    averageGwei: (Math.random() * 100 + 20).toFixed(5)
                },
                price: (Math.random() * 0.5 + 0.01).toFixed(3), // 0.01 - 0.51 ETH
                priceUSD: (Math.random() * 200 + 30).toFixed(2),
                rarity: Math.floor(Math.random() * 8610) + 1,
                totalSupply: 8610,
                tokenId: i.toString(),
                contractAddress: "******************************************"
            });
        }

        return nfts;
    }

    displayNFTs() {
        const nftGrid = document.getElementById('nft-grid');
        if (!nftGrid) return;

        nftGrid.innerHTML = '';

        this.featuredNFTs.forEach(nft => {
            const nftCard = document.createElement('div');
            nftCard.className = 'nft-card';

            const attributes = nft.attributes || {};
            const totalFees = attributes.totalFees || attributes.trait || 'N/A';
            const spenderLevel = attributes.spenderLevel || 'N/A';
            const rarityDisplay = nft.rarity ? `#${nft.rarity}/${nft.totalSupply || 8610}` : 'N/A';
            const priceDisplay = nft.price ? `${nft.price} ETH` : 'Not Listed';
            const priceUSDDisplay = nft.priceUSD ? `($${nft.priceUSD})` : '';

            nftCard.innerHTML = `
                <div class="nft-image">
                    <img src="${nft.image}" alt="${nft.name}" loading="lazy" onerror="this.src='./images/fees-wtf-nft.svg'">
                    <div class="nft-overlay">
                        <button class="nft-action-btn" onclick="window.nftManager.viewNFT('${nft.id}')">
                            View Details
                        </button>
                    </div>
                    <div class="nft-rarity-badge">
                        <span>${rarityDisplay}</span>
                    </div>
                </div>
                <div class="nft-info">
                    <h3 class="nft-name">${nft.name}</h3>
                    <div class="nft-traits">
                        <div class="trait-item">
                            <span class="trait-label">Total Fees:</span>
                            <span class="trait-value">${totalFees} ETH</span>
                        </div>
                        <div class="trait-item">
                            <span class="trait-label">Spender Level:</span>
                            <span class="trait-value">${spenderLevel}</span>
                        </div>
                    </div>
                    <div class="nft-price">
                        <span class="price-label">Price:</span>
                        <span class="price-value">${priceDisplay}</span>
                        ${priceUSDDisplay ? `<span class="price-usd">${priceUSDDisplay}</span>` : ''}
                    </div>
                </div>
            `;

            nftGrid.appendChild(nftCard);
        });

        this.addNFTStyles();
    }

    addNFTStyles() {
        if (document.getElementById('nft-styles')) return;

        const style = document.createElement('style');
        style.id = 'nft-styles';
        style.textContent = `
            .nft-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 24px;
                margin-top: 32px;
            }

            .nft-card {
                background: var(--card-bg);
                border-radius: 16px;
                overflow: hidden;
                border: 1px solid var(--border-color);
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .nft-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            }

            .nft-image {
                position: relative;
                aspect-ratio: 1;
                overflow: hidden;
            }

            .nft-image img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }

            .nft-card:hover .nft-image img {
                transform: scale(1.05);
            }

            .nft-rarity-badge {
                position: absolute;
                top: 8px;
                right: 8px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.75rem;
                font-weight: 600;
            }

            .nft-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .nft-card:hover .nft-overlay {
                opacity: 1;
            }

            .nft-action-btn {
                background: var(--primary-color);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: background 0.3s ease;
            }

            .nft-action-btn:hover {
                background: var(--primary-hover);
            }

            .nft-info {
                padding: 20px;
            }

            .nft-name {
                font-size: 18px;
                font-weight: 600;
                margin: 0 0 12px 0;
                color: var(--text-color);
            }

            .nft-traits {
                display: flex;
                flex-direction: column;
                gap: 6px;
                margin-bottom: 12px;
            }

            .trait-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 0;
                border-bottom: 1px solid var(--border-color);
            }

            .trait-item:last-child {
                border-bottom: none;
            }

            .trait-label {
                font-size: 0.75rem;
                color: var(--text-secondary);
                font-weight: 500;
            }

            .trait-value {
                font-size: 0.75rem;
                color: var(--text-primary);
                font-weight: 600;
            }

            .nft-trait, .nft-background {
                background: var(--accent-color);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }

            .nft-price, .nft-rarity {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }

            .price-label, .rarity-label {
                color: var(--text-secondary);
                font-size: 14px;
            }

            .price-value {
                color: var(--primary-color);
                font-weight: 600;
                font-size: 16px;
            }

            .price-usd {
                color: var(--text-secondary);
                font-size: 0.75rem;
                margin-left: 4px;
            }

            .rarity-value {
                color: var(--text-color);
                font-weight: 500;
            }
        `;

        document.head.appendChild(style);
    }

    async mintNFT() {
        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            if (window.feesWTFApp) {
                window.feesWTFApp.showError('Please connect your wallet to mint NFTs');
                window.feesWTFApp.showConnectModal();
            }
            return;
        }

        try {
            console.log('Minting NFT...');

            if (window.feesWTFApp) {
                window.feesWTFApp.showToast('Minting your Fees.WTF NFT...', 'wallet', 3000);
            }

            const txResult = await this.simulateTransaction();

            if (txResult.success && txResult.txHash) {
                if (window.feesWTFApp) {
                    window.feesWTFApp.showSuccess(`🎉 NFT minted successfully! TX: ${txResult.txHash.substring(0, 10)}...`);
                }

                setTimeout(async () => {
                    const verified = await this.verifyTransaction(txResult.txHash);
                    if (verified && window.feesWTFApp) {
                        window.feesWTFApp.showToast('✅ Transaction confirmed on blockchain!', 'success', 4000);
                    }
                }, 5000);
            } else {
                throw new Error('Minting transaction failed');
            }

            this.loadUserNFTs();

        } catch (error) {
            console.error('Error minting NFT:', error);
            if (window.feesWTFApp) {
                window.feesWTFApp.showError('Failed to mint NFT. Please try again.');
            }
        }
    }

    viewNFT(nftId) {
        const nft = this.featuredNFTs.find(n => n.id === nftId || n.id === parseInt(nftId));
        if (!nft) return;

        const modal = document.createElement('div');
        modal.className = 'nft-detail-modal';
        modal.innerHTML = `
            <div class="nft-detail-overlay">
                <div class="nft-detail-content">
                    <button class="nft-detail-close">&times;</button>
                    <div class="nft-detail-image">
                        <img src="${nft.image}" alt="${nft.name}">
                    </div>
                    <div class="nft-detail-info">
                        <h2>${nft.name}</h2>
                        <div class="nft-detail-description">
                            <p>${nft.description || 'Official fees.wtf NFT showing gas fee statistics'}</p>
                        </div>
                        <div class="nft-detail-traits">
                            ${nft.attributes ? Object.entries(nft.attributes).map(([key, value]) => `
                                <div class="trait-detail">
                                    <span class="trait-name">${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</span>
                                    <span class="trait-val">${value}</span>
                                </div>
                            `).join('') : ''}
                        </div>
                        <div class="nft-detail-price">
                            <span class="label">Current Price:</span>
                            <span class="value">${nft.price} ETH</span>
                            ${nft.priceUSD ? `<span class="usd-value">($${nft.priceUSD})</span>` : ''}
                        </div>
                        <div class="nft-detail-rarity">
                            <span class="label">Rarity Rank:</span>
                            <span class="value">#${nft.rarity}/${nft.totalSupply || 8610}</span>
                        </div>
                        <div class="nft-detail-contract">
                            <span class="label">Token ID:</span>
                            <span class="value">${nft.tokenId}</span>
                        </div>
                        <div class="nft-detail-actions">
                            <button class="nft-buy-btn">Buy on OpenSea</button>
                            <button class="nft-offer-btn">Make Offer</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const closeBtn = modal.querySelector('.nft-detail-close');
        const overlay = modal.querySelector('.nft-detail-overlay');
        const buyBtn = modal.querySelector('.nft-buy-btn');
        const offerBtn = modal.querySelector('.nft-offer-btn');

        closeBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                document.body.removeChild(modal);
            }
        });

        buyBtn.addEventListener('click', () => {
            const openseaUrl = `https://opensea.io/assets/ethereum/${nft.contractAddress || '******************************************'}/${nft.tokenId}`;
            window.open(openseaUrl, '_blank');
            document.body.removeChild(modal);
        });

        offerBtn.addEventListener('click', () => {
            this.makeOffer(nft);
            document.body.removeChild(modal);
        });

        this.addModalStyles();
    }

    addModalStyles() {
        if (document.getElementById('nft-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'nft-modal-styles';
        style.textContent = `
            .nft-detail-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 10000;
            }

            .nft-detail-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.85);
                backdrop-filter: blur(8px);
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                animation: fadeIn 0.3s ease-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translateY(30px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            .nft-detail-content {
                background: var(--bg-primary);
                border-radius: 20px;
                max-width: 900px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 40px;
                padding: 40px;
                position: relative;
                border: 1px solid var(--border-color);
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
                animation: slideUp 0.4s ease-out;
            }

            .nft-detail-close {
                position: absolute;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.5);
                border: none;
                font-size: 20px;
                color: white;
                cursor: pointer;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.3s ease;
                z-index: 10;
            }

            .nft-detail-close:hover {
                background: rgba(0, 0, 0, 0.8);
                transform: scale(1.1);
            }

            .nft-detail-image {
                position: relative;
                border-radius: 16px;
                overflow: hidden;
                background: var(--bg-secondary);
                border: 2px solid var(--border-color);
            }

            .nft-detail-image img {
                width: 100%;
                height: auto;
                display: block;
                border-radius: 14px;
                transition: transform 0.3s ease;
            }

            .nft-detail-image:hover img {
                transform: scale(1.02);
            }

            .nft-detail-info h2 {
                margin: 0 0 16px 0;
                color: var(--text-color);
                font-size: 24px;
                font-weight: 600;
            }

            .nft-detail-description {
                margin-bottom: 20px;
                padding: 12px;
                background: var(--bg-tertiary);
                border-radius: 8px;
                border-left: 4px solid var(--accent-color);
            }

            .nft-detail-description p {
                margin: 0;
                color: var(--text-secondary);
                font-size: 14px;
                line-height: 1.5;
            }

            .trait-detail {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                margin-bottom: 6px;
                background: var(--bg-secondary);
                border-radius: 6px;
                border: 1px solid var(--border-color);
            }

            .trait-name {
                font-size: 13px;
                color: var(--text-secondary);
                font-weight: 500;
            }

            .trait-val {
                font-size: 13px;
                color: var(--text-primary);
                font-weight: 600;
            }

            .usd-value {
                color: var(--text-secondary);
                font-size: 14px;
                margin-left: 8px;
            }

            .nft-detail-traits {
                display: flex;
                flex-direction: column;
                gap: 12px;
                margin-bottom: 24px;
                padding: 20px;
                background: var(--bg-secondary);
                border-radius: 12px;
                border: 1px solid var(--border-color);
            }

            .nft-detail-traits .trait,
            .nft-detail-traits .background {
                background: var(--accent-color);
                color: white;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }

            .nft-detail-price,
            .nft-detail-rarity {
                display: flex;
                justify-content: space-between;
                margin-bottom: 16px;
                padding: 12px 0;
                border-bottom: 1px solid var(--border-color);
            }

            .nft-detail-actions {
                display: flex;
                gap: 12px;
                margin-top: 24px;
            }

            .nft-buy-btn,
            .nft-offer-btn {
                flex: 1;
                padding: 16px 24px;
                border-radius: 12px;
                font-weight: 600;
                font-size: 16px;
                cursor: pointer;
                transition: all 0.3s ease;
                text-decoration: none;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            }

            .nft-buy-btn {
                background: linear-gradient(135deg, #3B82F6, #1D4ED8);
                color: white;
                border: none;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

            .nft-buy-btn:hover {
                background: linear-gradient(135deg, #2563EB, #1E40AF);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
            }

            .nft-offer-btn {
                background: transparent;
                color: var(--text-primary);
                border: 2px solid var(--border-color);
            }

            .nft-offer-btn:hover {
                background: var(--bg-secondary);
                border-color: var(--text-primary);
                transform: translateY(-2px);
            }

            @media (max-width: 768px) {
                .nft-detail-content {
                    grid-template-columns: 1fr;
                    gap: 24px;
                    padding: 24px;
                    margin: 10px;
                    max-height: 95vh;
                }

                .nft-detail-close {
                    top: 16px;
                    right: 16px;
                    width: 36px;
                    height: 36px;
                }

                .nft-buy-btn,
                .nft-offer-btn {
                    padding: 14px 20px;
                    font-size: 15px;
                }

                .nft-detail-traits {
                    padding: 16px;
                }
            }
        `;

        document.head.appendChild(style);
    }

    async buyNFT(nft) {
        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            if (window.feesWTFApp) {
                window.feesWTFApp.showError('Please connect your wallet to buy NFTs');
            }
            return;
        }

        try {
            console.log(`Buying NFT ${nft.name} for ${nft.price} ETH...`);
            
            await this.simulateTransaction();
            
            if (window.feesWTFApp) {
                window.feesWTFApp.showSuccess(`Successfully purchased ${nft.name}!`);
            }
            
        } catch (error) {
            console.error('Error buying NFT:', error);
            if (window.feesWTFApp) {
                window.feesWTFApp.showError('Failed to purchase NFT. Please try again.');
            }
        }
    }

    async makeOffer(nft) {
        console.log('Make offer functionality coming soon...');
        if (window.feesWTFApp) {
            window.feesWTFApp.showError('Make offer functionality coming soon');
        }
    }

    async loadUserNFTs() {
        if (!window.feesWTFApp || !window.feesWTFApp.isWalletConnected) {
            return;
        }

        try {
            console.log('Loading user NFTs...');
            
        } catch (error) {
            console.error('Error loading user NFTs:', error);
        }
    }

    async simulateTransaction() {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                const success = Math.random() > 0.1;
                if (success) {
                    const mockTxHash = '0x' + Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join('');
                    resolve({ txHash: mockTxHash, success: true });
                } else {
                    reject(new Error('Transaction simulation failed'));
                }
            }, 3000);
        });
    }

    async verifyTransaction(txHash) {
        if (!window.apiConfig || !txHash) {
            return false;
        }

        try {
            const receipt = await window.apiConfig.getTransactionReceipt(txHash);
            return receipt && receipt.status === '0x1';
        } catch (error) {
            console.error('Error verifying transaction:', error);
            return false;
        }
    }
}

window.nftManager = new NFTManager();

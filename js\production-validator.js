class ProductionValidator {
    constructor() {
        this.testResults = [];
        this.criticalErrors = [];
        this.warnings = [];
        this.isValidating = false;
    }

    async runFullValidation() {
        if (this.isValidating) {
            console.warn('Validation already in progress');
            return;
        }

        this.isValidating = true;
        this.testResults = [];
        this.criticalErrors = [];
        this.warnings = [];


        try {
            await this.validateAPIs();
            await this.validateWalletIntegration();
            await this.validateBlockchainConnectivity();
            await this.validateDataIntegrity();
            await this.validatePerformance();
            await this.validateSecurity();

            this.generateValidationReport();

        } catch (error) {
            console.error('Validation failed:', error);
            this.criticalErrors.push({
                test: 'Validation Suite',
                error: error.message,
                severity: 'critical'
            });
        } finally {
            this.isValidating = false;
        }
    }

    async validateAPIs() {
        console.log('📡 Validating API integrations...');

        const apiTests = [
            { name: 'Gas Price APIs', test: () => this.testGasAPIs() },
            { name: 'Price APIs', test: () => this.testPriceAPIs() },
            { name: 'DEX APIs', test: () => this.testDEXAPIs() },
            { name: 'NFT APIs', test: () => this.testNFTAPIs() }
        ];

        for (const apiTest of apiTests) {
            try {
                const result = await apiTest.test();
                this.testResults.push({
                    category: 'API',
                    test: apiTest.name,
                    status: result.success ? 'pass' : 'fail',
                    details: result.details,
                    responseTime: result.responseTime
                });
            } catch (error) {
                this.testResults.push({
                    category: 'API',
                    test: apiTest.name,
                    status: 'error',
                    error: error.message
                });
            }
        }
    }

    async testGasAPIs() {
        const startTime = performance.now();
        
        try {
            const etherscanData = await apiConfig.makeRequest('etherscan', apiConfig.endpoints.etherscan.gasPrice);
            
            if (!etherscanData || etherscanData.status !== '1') {
                throw new Error('Etherscan API returned invalid data');
            }

            let gasStationData = null;
            try {
                gasStationData = await apiConfig.makeRequest('ethGasStation', apiConfig.endpoints.ethGasStation.gasPrice);
            } catch (error) {
                // Silently handle ETH Gas Station API errors
            }

            return {
                success: true,
                details: {
                    etherscan: !!etherscanData,
                    gasStation: !!gasStationData,
                    gasPrice: etherscanData.result?.StandardGasPrice
                },
                responseTime: performance.now() - startTime
            };

        } catch (error) {
            return {
                success: false,
                details: { error: error.message },
                responseTime: performance.now() - startTime
            };
        }
    }

    async testPriceAPIs() {
        const startTime = performance.now();
        
        try {
            const priceData = await apiConfig.makeRequest('coingecko', apiConfig.endpoints.coingecko.ethPrice);
            
            if (!priceData || !priceData.ethereum) {
                throw new Error('CoinGecko API returned invalid data');
            }

            return {
                success: true,
                details: {
                    ethPrice: priceData.ethereum.usd,
                    change24h: priceData.ethereum.usd_24h_change
                },
                responseTime: performance.now() - startTime
            };

        } catch (error) {
            return {
                success: false,
                details: { error: error.message },
                responseTime: performance.now() - startTime
            };
        }
    }

    async testDEXAPIs() {
        const startTime = performance.now();
        
        try {
            let oneinchAvailable = false;
            try {
                const tokens = await apiConfig.makeRequest('oneinch', apiConfig.endpoints.oneinch.tokens);
                oneinchAvailable = !!tokens;
            } catch (error) {
                console.warn('1inch API not available:', error.message);
            }

            const dexData = await apiConfig.makeRequest('dexscreener', '/tokens/******************************************');

            return {
                success: true,
                details: {
                    oneinch: oneinchAvailable,
                    dexscreener: !!dexData
                },
                responseTime: performance.now() - startTime
            };

        } catch (error) {
            return {
                success: false,
                details: { error: error.message },
                responseTime: performance.now() - startTime
            };
        }
    }

    async testNFTAPIs() {
        const startTime = performance.now();
        
        try {
            let openseaAvailable = false;
            try {
                const collection = await apiConfig.makeRequest('opensea', '/collection/fees-wtf-nft');
                openseaAvailable = !!collection;
            } catch (error) {
                console.warn('OpenSea API not available:', error.message);
            }

            return {
                success: true,
                details: {
                    opensea: openseaAvailable
                },
                responseTime: performance.now() - startTime
            };

        } catch (error) {
            return {
                success: false,
                details: { error: error.message },
                responseTime: performance.now() - startTime
            };
        }
    }

    async validateWalletIntegration() {

        const walletTests = [
            { name: 'MetaMask Detection', test: () => this.testMetaMaskDetection() },
            { name: 'Web3 Provider', test: () => this.testWeb3Provider() },
            { name: 'Network Detection', test: () => this.testNetworkDetection() }
        ];

        for (const walletTest of walletTests) {
            try {
                const result = await walletTest.test();
                this.testResults.push({
                    category: 'Wallet',
                    test: walletTest.name,
                    status: result.success ? 'pass' : 'fail',
                    details: result.details
                });
            } catch (error) {
                this.testResults.push({
                    category: 'Wallet',
                    test: walletTest.name,
                    status: 'error',
                    error: error.message
                });
            }
        }
    }

    async testMetaMaskDetection() {
        const metamaskAvailable = typeof window.ethereum !== 'undefined' && window.ethereum.isMetaMask;
        
        return {
            success: true,
            details: {
                metamaskInstalled: metamaskAvailable,
                ethereumProvider: typeof window.ethereum !== 'undefined'
            }
        };
    }

    async testWeb3Provider() {
        if (!window.ethereum) {
            return {
                success: false,
                details: { error: 'No Web3 provider available' }
            };
        }

        try {
            const chainId = await window.ethereum.request({ method: 'eth_chainId' });
            const accounts = await window.ethereum.request({ method: 'eth_accounts' });

            return {
                success: true,
                details: {
                    chainId: parseInt(chainId, 16),
                    connectedAccounts: accounts.length,
                    providerReady: true
                }
            };
        } catch (error) {
            return {
                success: false,
                details: { error: error.message }
            };
        }
    }

    async testNetworkDetection() {
        const expectedChainId = window.productionConfig?.contracts?.chainId || 1;
        
        try {
            const chainId = await window.ethereum.request({ method: 'eth_chainId' });
            const currentChainId = parseInt(chainId, 16);
            
            return {
                success: currentChainId === expectedChainId,
                details: {
                    currentChainId,
                    expectedChainId,
                    correctNetwork: currentChainId === expectedChainId
                }
            };
        } catch (error) {
            return {
                success: false,
                details: { error: error.message }
            };
        }
    }

    async validateBlockchainConnectivity() {
        console.log('⛓️ Validating blockchain connectivity...');

        if (!window.ethereum) {
            this.warnings.push('No Web3 provider available for blockchain tests');
            return;
        }

        try {
            const blockNumber = await window.ethereum.request({
                method: 'eth_blockNumber'
            });

            this.testResults.push({
                category: 'Blockchain',
                test: 'Block Number Fetch',
                status: 'pass',
                details: {
                    blockNumber: parseInt(blockNumber, 16),
                    timestamp: new Date().toISOString()
                }
            });

            if (window.productionConfig?.contracts) {
                await this.testContractCalls();
            }

        } catch (error) {
            this.testResults.push({
                category: 'Blockchain',
                test: 'Blockchain Connectivity',
                status: 'error',
                error: error.message
            });
        }
    }

    async testContractCalls() {
        const contracts = window.productionConfig.contracts;
        
        if (contracts.wtfToken) {
            try {
                const totalSupply = await window.ethereum.request({
                    method: 'eth_call',
                    params: [{
                        to: contracts.wtfToken,
                        data: '0x18160ddd' 
                    }, 'latest']
                });

                this.testResults.push({
                    category: 'Blockchain',
                    test: 'WTF Token Contract',
                    status: 'pass',
                    details: {
                        totalSupply: parseInt(totalSupply, 16),
                        contractAddress: contracts.wtfToken
                    }
                });
            } catch (error) {
                this.testResults.push({
                    category: 'Blockchain',
                    test: 'WTF Token Contract',
                    status: 'error',
                    error: error.message
                });
            }
        }
    }

    async validateDataIntegrity() {

        try {
            const gasData = await window.app?.gasTracker?.getCurrentGasData();
            const ethPrice = await window.app?.gasTracker?.getCurrentETHPrice();

            this.testResults.push({
                category: 'Data',
                test: 'Gas Data Integrity',
                status: gasData && gasData.standard > 0 ? 'pass' : 'fail',
                details: gasData
            });

            this.testResults.push({
                category: 'Data',
                test: 'ETH Price Integrity',
                status: ethPrice && ethPrice.usd > 0 ? 'pass' : 'fail',
                details: ethPrice
            });

        } catch (error) {
            this.testResults.push({
                category: 'Data',
                test: 'Data Integrity',
                status: 'error',
                error: error.message
            });
        }
    }

    async validatePerformance() {

        const performanceMetrics = window.utils?.performanceMetrics || new Map();
        
        for (const [label, metric] of performanceMetrics) {
            const isAcceptable = metric.duration < 5000; 
            
            this.testResults.push({
                category: 'Performance',
                test: `${label} Performance`,
                status: isAcceptable ? 'pass' : 'warning',
                details: {
                    duration: metric.duration,
                    threshold: 5000,
                    acceptable: isAcceptable
                }
            });
        }
    }

    async validateSecurity() {
        console.log('🔒 Validating security...');

        const isHTTPS = window.location.protocol === 'https:';
        const isProduction = window.productionConfig?.isProduction();

        if (isProduction && !isHTTPS) {
            this.criticalErrors.push({
                test: 'HTTPS Security',
                error: 'Production site must use HTTPS',
                severity: 'critical'
            });
        }

        this.testResults.push({
            category: 'Security',
            test: 'HTTPS Protocol',
            status: (!isProduction || isHTTPS) ? 'pass' : 'fail',
            details: {
                protocol: window.location.protocol,
                isProduction,
                secure: isHTTPS
            }
        });
    }

    generateValidationReport() {
       
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.status === 'pass').length;
        const failedTests = this.testResults.filter(r => r.status === 'fail').length;
        const errorTests = this.testResults.filter(r => r.status === 'error').length;

      
        const categories = [...new Set(this.testResults.map(r => r.category))];
        
        categories.forEach(category => {
            console.log(`\n${category} Tests:`);
            const categoryTests = this.testResults.filter(r => r.category === category);
            
            categoryTests.forEach(test => {
                const icon = test.status === 'pass' ? '✅' : test.status === 'fail' ? '❌' : '🚨';
                console.log(`  ${icon} ${test.test}`);
                
                if (test.error) {
                    console.log(`    Error: ${test.error}`);
                }
                
                if (test.responseTime) {
                    console.log(`    Response Time: ${test.responseTime.toFixed(2)}ms`);
                }
            });
        });

        if (this.criticalErrors.length > 0) {
            console.log('\n🔥 CRITICAL ERRORS:');
            this.criticalErrors.forEach(error => {
                console.log(`  - ${error.test}: ${error.error}`);
            });
        }

        if (this.warnings.length > 0) {
            console.log('\n⚠️ WARNINGS:');
            this.warnings.forEach(warning => {
                console.log(`  - ${warning}`);
            });
        }

        const overallStatus = this.criticalErrors.length === 0 && errorTests === 0 ? 
            (failedTests === 0 ? 'READY FOR PRODUCTION' : 'NEEDS ATTENTION') : 
            'NOT READY FOR PRODUCTION';

        console.log(`\n🎯 Overall Status: ${overallStatus}`);
        console.log('='.repeat(50));

        return {
            status: overallStatus,
            summary: { totalTests, passedTests, failedTests, errorTests },
            criticalErrors: this.criticalErrors,
            warnings: this.warnings,
            results: this.testResults
        };
    }
}

window.productionValidator = new ProductionValidator();

if (window.productionConfig?.isDevelopment()) {
    setTimeout(() => {
        window.productionValidator.runFullValidation();
    }, 3000);
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductionValidator;
}

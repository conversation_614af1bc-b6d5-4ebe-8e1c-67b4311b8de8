/* Social Sharing Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.3s ease;
}

.modal-overlay.hidden {
    display: none;
}

.modal-content {
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
}

.sharing-modal {
    max-width: 480px;
    width: 90%;
    background: linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    color: white;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05);
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.sharing-modal .modal-header {
    background: rgba(0, 0, 0, 0.3);
    padding: 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sharing-modal .modal-header h2 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sharing-modal .close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #ffffff;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sharing-modal .close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.sharing-content {
    padding: 32px 24px;
    text-align: center;
}

.drip-logo-3d {
    margin-bottom: 24px;
    perspective: 1000px;
}

.logo-3d {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: rotateY(15deg) rotateX(10deg);
    transition: transform 0.3s ease;
    animation: float 3s ease-in-out infinite;
}

.logo-3d:hover {
    transform: rotateY(0deg) rotateX(0deg) scale(1.1);
}

@keyframes float {
    0%, 100% { transform: rotateY(15deg) rotateX(10deg) translateY(0px); }
    50% { transform: rotateY(15deg) rotateX(10deg) translateY(-10px); }
}

/* Animation for success message */
@keyframes slideIn {
    0% {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification animations */
@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(100px);
    }
}

/* Bounce animation for success */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .sharing-header {
        text-align: center !important;
    }

    .drip-logo-small {
        display: flex !important;
        justify-content: center !important;
        margin-bottom: 16px !important;
    }

    .sharing-interface {
        padding: 16px !important;
    }

    .tweet-compose {
        margin: 16px 0 !important;
        padding: 16px !important;
    }
}

.sharing-content h3 {
    margin: 24px 0 12px;
    font-size: 1.25rem;
    font-weight: 600;
    color: #ffffff;
}

.sharing-content p {
    margin-bottom: 28px;
    opacity: 0.85;
    line-height: 1.6;
    color: #e0e0e0;
}

.tweet-preview {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 12px;
    padding: 20px;
    margin: 28px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.tweet-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    font-weight: 500;
    color: #ffffff;
}

.twitter-icon {
    font-size: 1.1rem;
    color: #1da1f2;
}

.tweet-content {
    background: rgba(0, 0, 0, 0.3);
    padding: 16px;
    border-radius: 8px;
    text-align: left;
    line-height: 1.5;
    font-size: 0.9rem;
    border: 1px solid rgba(255, 255, 255, 0.08);
    color: #f0f0f0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.sharing-actions {
    margin-top: 32px;
}

.share-btn {
    background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s ease;
    box-shadow: 0 8px 20px rgba(29, 161, 242, 0.3);
    border: 1px solid rgba(29, 161, 242, 0.2);
}

.share-btn:hover {
    background: linear-gradient(135deg, #0d8bd9 0%, #1da1f2 100%);
    transform: translateY(-1px);
    box-shadow: 0 12px 30px rgba(29, 161, 242, 0.4);
}

.btn-icon {
    font-size: 1.1rem;
}

.sharing-note {
    margin-top: 16px;
    font-size: 0.85rem;
    opacity: 0.7;
    color: #b0b0b0;
}

/* Sharing Status Styles */
.sharing-status {
    background: linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 32px;
    margin: 24px 0;
    text-align: center;
    color: white;
    backdrop-filter: blur(10px);
}

.waiting-content h3 {
    margin: 20px 0 10px;
    font-size: 1.3rem;
}

.waiting-content p {
    margin-bottom: 25px;
    opacity: 0.9;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.check-btn, .verify-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.check-btn:hover, .verify-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

.verification-message {
    text-align: center;
    padding: 20px;
}

.verification-message h3 {
    margin-bottom: 15px;
    color: #667eea;
}

/* Unlock Success Animation */
.unlock-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    text-align: center;
    color: white;
    animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-animation {
    font-size: 3rem;
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    80% {
        transform: translateY(-10px);
    }
}

.unlock-success h3 {
    margin: 15px 0 10px;
    font-size: 1.4rem;
}

.unlock-success p {
    margin: 0;
    opacity: 0.9;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sharing-modal {
        width: 95%;
        margin: 20px;
    }
    
    .sharing-content {
        padding: 20px 15px;
    }
    
    .logo-3d {
        width: 60px;
        height: 60px;
    }
    
    .tweet-preview {
        padding: 15px;
    }
    
    .share-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }
}
